2025-07-14 00:02:43,063 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:02:43,064 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:02:43,066 [INFO] Loaded cog: embedgen.py
2025-07-14 00:02:43,067 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:02:43,070 [INFO] Loaded cog: giveaway.py
2025-07-14 00:02:43,073 [INFO] Loaded cog: moderation.py
2025-07-14 00:02:43,075 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:02:43,076 [INFO] Loaded cog: rosters.py
2025-07-14 00:02:43,078 [INFO] Loaded cog: shipgame.py
2025-07-14 00:02:43,080 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:02:43,109 [ERROR] Failed to load cog traderoutes.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 1055, in setup
    await bot.add_cog(TradeRoutes(bot))
                     ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 105, in __init__
    self.update_data_extract.start()
    ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TradeRoutes' object has no attribute 'update_data_extract'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.traderoutes' raised an error: AttributeError: 'TradeRoutes' object has no attribute 'update_data_extract'

2025-07-14 00:02:43,113 [INFO] Loaded cog: voice.py
2025-07-14 00:02:43,115 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:02:43,116 [INFO] Loaded cog: welcome.py
2025-07-14 00:02:43,116 [INFO] logging in using static token
2025-07-14 00:02:43,986 [INFO] Shard ID None has connected to Gateway (Session ID: 53235a05cb24072828a0b7610bf2fbb0).
2025-07-14 00:02:46,010 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:02:46,226 [INFO] Synced 29 slash commands.
2025-07-14 00:03:43,782 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:03:43,785 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:03:43,786 [INFO] Loaded cog: embedgen.py
2025-07-14 00:03:43,788 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:03:43,791 [INFO] Loaded cog: giveaway.py
2025-07-14 00:03:43,793 [INFO] Loaded cog: moderation.py
2025-07-14 00:03:43,794 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:03:43,796 [INFO] Loaded cog: rosters.py
2025-07-14 00:03:43,797 [INFO] Loaded cog: shipgame.py
2025-07-14 00:03:43,797 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:03:43,814 [ERROR] Failed to load cog traderoutes.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 1055, in setup
    await bot.add_cog(TradeRoutes(bot))
                     ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 105, in __init__
    self.update_uex_data.start()
    ^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TradeRoutes' object has no attribute 'update_uex_data'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.traderoutes' raised an error: AttributeError: 'TradeRoutes' object has no attribute 'update_uex_data'

2025-07-14 00:03:43,816 [INFO] Loaded cog: voice.py
2025-07-14 00:03:43,821 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:03:43,822 [INFO] Loaded cog: welcome.py
2025-07-14 00:03:43,822 [INFO] logging in using static token
2025-07-14 00:03:44,532 [INFO] Shard ID None has connected to Gateway (Session ID: 4a686e7a3a6d1921b1f41af53d1c2b02).
2025-07-14 00:03:46,543 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:03:46,796 [INFO] Synced 29 slash commands.
2025-07-14 00:04:43,880 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:04:43,882 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:04:43,883 [INFO] Loaded cog: embedgen.py
2025-07-14 00:04:43,885 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:04:43,887 [INFO] Loaded cog: giveaway.py
2025-07-14 00:04:43,890 [INFO] Loaded cog: moderation.py
2025-07-14 00:04:43,891 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:04:43,892 [INFO] Loaded cog: rosters.py
2025-07-14 00:04:43,893 [INFO] Loaded cog: shipgame.py
2025-07-14 00:04:43,894 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:04:43,910 [ERROR] Failed to load cog traderoutes.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 1055, in setup
    await bot.add_cog(TradeRoutes(bot))
                     ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 105, in __init__
    self.update_data_extract.start()
    ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TradeRoutes' object has no attribute 'update_data_extract'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.traderoutes' raised an error: AttributeError: 'TradeRoutes' object has no attribute 'update_data_extract'

2025-07-14 00:04:43,912 [INFO] Loaded cog: voice.py
2025-07-14 00:04:43,914 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:04:43,915 [INFO] Loaded cog: welcome.py
2025-07-14 00:04:43,915 [INFO] logging in using static token
2025-07-14 00:04:44,718 [INFO] Shard ID None has connected to Gateway (Session ID: 4a16389f58a0c8ac542c5a9ff60607ea).
2025-07-14 00:04:46,738 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:04:47,051 [INFO] Synced 29 slash commands.
2025-07-14 00:05:55,622 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:05:55,623 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:05:55,625 [INFO] Loaded cog: embedgen.py
2025-07-14 00:05:55,626 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:05:55,629 [INFO] Loaded cog: giveaway.py
2025-07-14 00:05:55,631 [INFO] Loaded cog: moderation.py
2025-07-14 00:05:55,633 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:05:55,634 [INFO] Loaded cog: rosters.py
2025-07-14 00:05:55,636 [INFO] Loaded cog: shipgame.py
2025-07-14 00:05:55,637 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:05:55,656 [ERROR] Failed to load cog traderoutes.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 1055, in setup
    await bot.add_cog(TradeRoutes(bot))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 783, in add_cog
    cog = await cog._inject(self, override=override, guild=guild, guilds=guilds)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\cog.py", line 684, in _inject
    await maybe_coroutine(self.cog_load)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\utils.py", line 693, in maybe_coroutine
    return await value
           ^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 241, in cog_load
    await self.update_data_extract()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\tasks\__init__.py", line 368, in __call__
    return await self.coro(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 232, in update_data_extract
    await self.fetch_and_cache_commodities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TradeRoutes' object has no attribute 'fetch_and_cache_commodities'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.traderoutes' raised an error: AttributeError: 'TradeRoutes' object has no attribute 'fetch_and_cache_commodities'

2025-07-14 00:05:55,658 [INFO] Loaded cog: voice.py
2025-07-14 00:05:55,660 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:05:55,660 [INFO] Loaded cog: welcome.py
2025-07-14 00:05:55,660 [INFO] logging in using static token
2025-07-14 00:05:55,665 [ERROR] Unhandled exception in internal background task 'update_data_extract'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\tasks\__init__.py", line 239, in _loop
    await self.coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 232, in update_data_extract
    await self.fetch_and_cache_commodities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TradeRoutes' object has no attribute 'fetch_and_cache_commodities'
2025-07-14 00:05:56,404 [INFO] Shard ID None has connected to Gateway (Session ID: fc2e8a068383a40ea97998683c432470).
2025-07-14 00:05:58,414 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:05:58,813 [INFO] Synced 29 slash commands.
2025-07-14 00:07:38,092 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:07:38,094 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:07:38,095 [INFO] Loaded cog: embedgen.py
2025-07-14 00:07:38,097 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:07:38,100 [INFO] Loaded cog: giveaway.py
2025-07-14 00:07:38,103 [INFO] Loaded cog: moderation.py
2025-07-14 00:07:38,104 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:07:38,105 [INFO] Loaded cog: rosters.py
2025-07-14 00:07:38,107 [INFO] Loaded cog: shipgame.py
2025-07-14 00:07:38,108 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:07:38,121 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:07:38,123 [INFO] Loaded cog: voice.py
2025-07-14 00:07:38,125 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:07:38,126 [INFO] Loaded cog: welcome.py
2025-07-14 00:07:38,126 [INFO] logging in using static token
2025-07-14 00:07:38,892 [INFO] Shard ID None has connected to Gateway (Session ID: a69c183e7ed1f9274b1e40addf3ab11e).
2025-07-14 00:07:40,918 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:07:41,137 [INFO] Synced 29 slash commands.
2025-07-14 00:08:55,988 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:08:55,989 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:08:55,991 [INFO] Loaded cog: embedgen.py
2025-07-14 00:08:55,992 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:08:55,997 [INFO] Loaded cog: giveaway.py
2025-07-14 00:08:56,000 [INFO] Loaded cog: moderation.py
2025-07-14 00:08:56,001 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:08:56,003 [INFO] Loaded cog: rosters.py
2025-07-14 00:08:56,004 [INFO] Loaded cog: shipgame.py
2025-07-14 00:08:56,004 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:08:56,008 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:08:56,009 [INFO] Loaded cog: voice.py
2025-07-14 00:08:56,011 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:08:56,011 [INFO] Loaded cog: welcome.py
2025-07-14 00:08:56,011 [INFO] logging in using static token
2025-07-14 00:08:56,859 [INFO] Shard ID None has connected to Gateway (Session ID: 9c2adf7cd23e7363a724405e595605be).
2025-07-14 00:08:58,876 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:08:59,130 [INFO] Synced 29 slash commands.
2025-07-14 00:10:55,059 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:10:55,060 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:10:55,061 [INFO] Loaded cog: embedgen.py
2025-07-14 00:10:55,064 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:10:55,066 [INFO] Loaded cog: giveaway.py
2025-07-14 00:10:55,071 [INFO] Loaded cog: moderation.py
2025-07-14 00:10:55,072 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:10:55,074 [INFO] Loaded cog: rosters.py
2025-07-14 00:10:55,075 [INFO] Loaded cog: shipgame.py
2025-07-14 00:10:55,076 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:10:55,089 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:10:55,091 [INFO] Loaded cog: voice.py
2025-07-14 00:10:55,093 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:10:55,093 [INFO] Loaded cog: welcome.py
2025-07-14 00:10:55,094 [INFO] logging in using static token
2025-07-14 00:10:55,988 [INFO] Shard ID None has connected to Gateway (Session ID: 66714dd0b343cd97125b64580d51d80c).
2025-07-14 00:10:58,002 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:10:58,232 [INFO] Synced 29 slash commands.
2025-07-14 00:13:00,574 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:13:00,576 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:13:00,577 [INFO] Loaded cog: embedgen.py
2025-07-14 00:13:00,578 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:13:00,581 [INFO] Loaded cog: giveaway.py
2025-07-14 00:13:00,583 [INFO] Loaded cog: moderation.py
2025-07-14 00:13:00,584 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:13:00,586 [INFO] Loaded cog: rosters.py
2025-07-14 00:13:00,587 [INFO] Loaded cog: shipgame.py
2025-07-14 00:13:00,588 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:13:00,591 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:13:00,593 [INFO] Loaded cog: voice.py
2025-07-14 00:13:00,595 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:13:00,595 [INFO] Loaded cog: welcome.py
2025-07-14 00:13:00,595 [INFO] logging in using static token
2025-07-14 00:13:02,849 [INFO] Shard ID None has connected to Gateway (Session ID: 59003e59c259dd8f8517e19ae720b587).
2025-07-14 00:13:04,867 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:13:05,140 [INFO] Synced 29 slash commands globally.
2025-07-14 00:13:05,140 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:15:00,752 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:15:00,754 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:15:00,756 [INFO] Loaded cog: embedgen.py
2025-07-14 00:15:00,757 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:15:00,760 [INFO] Loaded cog: giveaway.py
2025-07-14 00:15:00,763 [INFO] Loaded cog: moderation.py
2025-07-14 00:15:00,767 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:15:00,769 [INFO] Loaded cog: rosters.py
2025-07-14 00:15:00,771 [INFO] Loaded cog: shipgame.py
2025-07-14 00:15:00,772 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:15:00,784 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:15:00,786 [INFO] Loaded cog: voice.py
2025-07-14 00:15:00,788 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:15:00,788 [INFO] Loaded cog: welcome.py
2025-07-14 00:15:00,788 [INFO] logging in using static token
2025-07-14 00:15:01,688 [INFO] Shard ID None has connected to Gateway (Session ID: b064717cb48878e7d451f110bba4f01b).
2025-07-14 00:15:03,704 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:15:04,104 [INFO] Synced 31 slash commands globally.
2025-07-14 00:15:04,105 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:16:54,552 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:16:54,554 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:16:54,555 [INFO] Loaded cog: embedgen.py
2025-07-14 00:16:54,556 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:16:54,560 [INFO] Loaded cog: giveaway.py
2025-07-14 00:16:54,562 [INFO] Loaded cog: moderation.py
2025-07-14 00:16:54,563 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:16:54,565 [INFO] Loaded cog: rosters.py
2025-07-14 00:16:54,566 [INFO] Loaded cog: shipgame.py
2025-07-14 00:16:54,567 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:16:54,581 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:16:54,582 [INFO] Loaded cog: voice.py
2025-07-14 00:16:54,584 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:16:54,584 [INFO] Loaded cog: welcome.py
2025-07-14 00:16:54,584 [INFO] logging in using static token
2025-07-14 00:16:55,266 [INFO] Shard ID None has connected to Gateway (Session ID: 042c1de51c64e1b2aeb2254566ae70d3).
2025-07-14 00:16:57,275 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:16:57,526 [INFO] Synced 31 slash commands globally.
2025-07-14 00:16:57,526 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:18:34,291 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:18:34,292 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:18:34,294 [INFO] Loaded cog: embedgen.py
2025-07-14 00:18:34,295 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:18:34,298 [INFO] Loaded cog: giveaway.py
2025-07-14 00:18:34,300 [INFO] Loaded cog: moderation.py
2025-07-14 00:18:34,301 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:18:34,303 [INFO] Loaded cog: rosters.py
2025-07-14 00:18:34,304 [INFO] Loaded cog: shipgame.py
2025-07-14 00:18:34,305 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:18:34,318 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:18:34,319 [INFO] Loaded cog: voice.py
2025-07-14 00:18:34,322 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:18:34,322 [INFO] Loaded cog: welcome.py
2025-07-14 00:18:34,322 [INFO] logging in using static token
2025-07-14 00:18:35,094 [INFO] Shard ID None has connected to Gateway (Session ID: 27558d3bdd4aadc5a4e72362496c1f47).
2025-07-14 00:18:37,121 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:18:37,359 [INFO] Synced 31 slash commands globally.
2025-07-14 00:18:37,360 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:20:13,252 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:20:13,254 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:20:13,255 [INFO] Loaded cog: embedgen.py
2025-07-14 00:20:13,256 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:20:13,260 [INFO] Loaded cog: giveaway.py
2025-07-14 00:20:13,262 [INFO] Loaded cog: moderation.py
2025-07-14 00:20:13,263 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:20:13,268 [INFO] Loaded cog: rosters.py
2025-07-14 00:20:13,269 [INFO] Loaded cog: shipgame.py
2025-07-14 00:20:13,270 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:20:13,284 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:20:13,285 [INFO] Loaded cog: voice.py
2025-07-14 00:20:13,287 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:20:13,288 [INFO] Loaded cog: welcome.py
2025-07-14 00:20:13,288 [INFO] logging in using static token
2025-07-14 00:20:15,281 [INFO] Shard ID None has connected to Gateway (Session ID: 9e18abb38a7ebda40763144a55b4f397).
2025-07-14 00:20:17,307 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:20:17,499 [INFO] Synced 31 slash commands globally.
2025-07-14 00:20:17,499 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:24:19,729 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:24:19,730 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:24:19,731 [INFO] Loaded cog: embedgen.py
2025-07-14 00:24:19,733 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:24:19,736 [INFO] Loaded cog: giveaway.py
2025-07-14 00:24:19,738 [INFO] Loaded cog: moderation.py
2025-07-14 00:24:19,739 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:24:19,741 [INFO] Loaded cog: rosters.py
2025-07-14 00:24:19,742 [INFO] Loaded cog: shipgame.py
2025-07-14 00:24:19,744 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:24:19,756 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:24:19,757 [INFO] Loaded cog: voice.py
2025-07-14 00:24:19,759 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:24:19,759 [INFO] Loaded cog: welcome.py
2025-07-14 00:24:19,759 [INFO] logging in using static token
2025-07-14 00:24:20,489 [INFO] Shard ID None has connected to Gateway (Session ID: 74b4f629f14979abad520a44cb64504e).
2025-07-14 00:24:22,504 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:24:22,799 [INFO] Synced 31 slash commands globally.
2025-07-14 00:24:22,799 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:27:31,875 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:27:31,877 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:27:31,878 [INFO] Loaded cog: embedgen.py
2025-07-14 00:27:31,880 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:27:31,883 [INFO] Loaded cog: giveaway.py
2025-07-14 00:27:31,886 [INFO] Loaded cog: moderation.py
2025-07-14 00:27:31,887 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:27:31,890 [INFO] Loaded cog: rosters.py
2025-07-14 00:27:31,891 [INFO] Loaded cog: shipgame.py
2025-07-14 00:27:31,891 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:27:31,905 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:27:31,907 [INFO] Loaded cog: voice.py
2025-07-14 00:27:31,909 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:27:31,909 [INFO] Loaded cog: welcome.py
2025-07-14 00:27:31,909 [INFO] logging in using static token
2025-07-14 00:27:32,674 [INFO] Shard ID None has connected to Gateway (Session ID: 2f31ff44564ebabbff08a4f89ed5a28f).
2025-07-14 00:27:34,692 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:27:34,939 [INFO] Synced 31 slash commands globally.
2025-07-14 00:27:34,939 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:32:01,752 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:32:01,754 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:32:01,755 [INFO] Loaded cog: embedgen.py
2025-07-14 00:32:01,757 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:32:01,760 [INFO] Loaded cog: giveaway.py
2025-07-14 00:32:01,763 [INFO] Loaded cog: moderation.py
2025-07-14 00:32:01,764 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:32:01,765 [INFO] Loaded cog: rosters.py
2025-07-14 00:32:01,767 [INFO] Loaded cog: shipgame.py
2025-07-14 00:32:01,768 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:32:01,786 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:32:01,787 [INFO] Loaded cog: voice.py
2025-07-14 00:32:01,790 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:32:01,791 [INFO] Loaded cog: welcome.py
2025-07-14 00:32:01,791 [INFO] logging in using static token
2025-07-14 00:32:02,574 [INFO] Shard ID None has connected to Gateway (Session ID: c76d2a64f608b988193a2993b50d12dd).
2025-07-14 00:32:04,582 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:32:04,813 [INFO] Synced 31 slash commands globally.
2025-07-14 00:32:04,813 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:34:11,113 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:34:11,114 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:34:11,115 [INFO] Loaded cog: embedgen.py
2025-07-14 00:34:11,117 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:34:11,119 [INFO] Loaded cog: giveaway.py
2025-07-14 00:34:11,124 [INFO] Loaded cog: moderation.py
2025-07-14 00:34:11,127 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:34:11,129 [INFO] Loaded cog: rosters.py
2025-07-14 00:34:11,130 [INFO] Loaded cog: shipgame.py
2025-07-14 00:34:11,131 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:34:11,148 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:34:11,150 [INFO] Loaded cog: voice.py
2025-07-14 00:34:11,152 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:34:11,152 [INFO] Loaded cog: welcome.py
2025-07-14 00:34:11,152 [INFO] logging in using static token
2025-07-14 00:34:11,993 [INFO] Shard ID None has connected to Gateway (Session ID: f2af4d9dcc87da2b93e42835788156e8).
2025-07-14 00:34:14,024 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:34:14,452 [INFO] Synced 31 slash commands globally.
2025-07-14 00:34:14,452 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:54:45,955 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:54:45,956 [INFO] Loaded cog: embedgen.py
2025-07-14 00:54:45,957 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:54:45,960 [INFO] Loaded cog: giveaway.py
2025-07-14 00:54:45,963 [INFO] Loaded cog: moderation.py
2025-07-14 00:54:45,964 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:54:45,966 [INFO] Loaded cog: rosters.py
2025-07-14 00:54:45,967 [INFO] Loaded cog: shipgame.py
2025-07-14 00:54:45,968 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:54:46,438 [INFO] UEX cache refreshed successfully
2025-07-14 00:54:46,438 [INFO] Loaded cog: uex_trade.py
2025-07-14 00:54:46,440 [INFO] Loaded cog: voice.py
2025-07-14 00:54:46,444 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:54:46,444 [INFO] Loaded cog: welcome.py
2025-07-14 00:54:46,445 [INFO] logging in using static token
2025-07-14 00:54:47,586 [INFO] Shard ID None has connected to Gateway (Session ID: 687fb240cc9274d00cebc24284896c44).
2025-07-14 00:54:49,595 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:54:50,072 [INFO] Synced 29 slash commands globally.
2025-07-14 00:54:50,073 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:55:37,000 [ERROR] UEX API request failed: 400, message='Bad Request', url='https://api.uexcorp.space/2.0/commodities_routes?investment=2000000'
2025-07-14 00:55:37,001 [ERROR] Error setting investment filter: Failed to connect to UEX API: 400, message='Bad Request', url='https://api.uexcorp.space/2.0/commodities_routes?investment=2000000'
2025-07-14 00:57:09,011 [INFO] UEX cache refreshed successfully
2025-07-14 01:01:42,035 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:01:42,036 [INFO] Loaded cog: embedgen.py
2025-07-14 01:01:42,037 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:01:42,040 [INFO] Loaded cog: giveaway.py
2025-07-14 01:01:42,045 [INFO] Loaded cog: moderation.py
2025-07-14 01:01:42,046 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:01:42,047 [INFO] Loaded cog: rosters.py
2025-07-14 01:01:42,048 [INFO] Loaded cog: shipgame.py
2025-07-14 01:01:42,049 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:01:42,465 [INFO] UEX cache refreshed successfully
2025-07-14 01:01:42,465 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:01:42,466 [INFO] Loaded cog: voice.py
2025-07-14 01:01:42,469 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:01:42,469 [INFO] Loaded cog: welcome.py
2025-07-14 01:01:42,469 [INFO] logging in using static token
2025-07-14 01:01:43,276 [INFO] Shard ID None has connected to Gateway (Session ID: e14dc2e153bf6ba72cb13e4f89fb9b4e).
2025-07-14 01:01:45,282 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:01:45,524 [INFO] Synced 29 slash commands globally.
2025-07-14 01:01:45,524 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:05:17,406 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:05:17,408 [INFO] Loaded cog: embedgen.py
2025-07-14 01:05:17,409 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:05:17,411 [INFO] Loaded cog: giveaway.py
2025-07-14 01:05:17,414 [INFO] Loaded cog: moderation.py
2025-07-14 01:05:17,415 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:05:17,417 [INFO] Loaded cog: rosters.py
2025-07-14 01:05:17,418 [INFO] Loaded cog: shipgame.py
2025-07-14 01:05:17,418 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:05:17,798 [INFO] UEX cache refreshed successfully
2025-07-14 01:05:17,798 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:05:17,800 [INFO] Loaded cog: voice.py
2025-07-14 01:05:17,803 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:05:17,804 [INFO] Loaded cog: welcome.py
2025-07-14 01:05:17,804 [INFO] logging in using static token
2025-07-14 01:05:18,699 [INFO] Shard ID None has connected to Gateway (Session ID: b9d564e1323fbdb6798a85ff938a86e3).
2025-07-14 01:05:20,711 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:05:21,077 [INFO] Synced 29 slash commands globally.
2025-07-14 01:05:21,077 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:07:48,494 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:07:48,495 [INFO] Loaded cog: embedgen.py
2025-07-14 01:07:48,497 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:07:48,499 [INFO] Loaded cog: giveaway.py
2025-07-14 01:07:48,502 [INFO] Loaded cog: moderation.py
2025-07-14 01:07:48,504 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:07:48,506 [INFO] Loaded cog: rosters.py
2025-07-14 01:07:48,508 [INFO] Loaded cog: shipgame.py
2025-07-14 01:07:48,509 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:07:48,942 [INFO] UEX cache refreshed successfully
2025-07-14 01:07:48,942 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:07:48,944 [INFO] Loaded cog: voice.py
2025-07-14 01:07:48,947 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:07:48,947 [INFO] Loaded cog: welcome.py
2025-07-14 01:07:48,948 [INFO] logging in using static token
2025-07-14 01:07:50,021 [INFO] Shard ID None has connected to Gateway (Session ID: eba2173641665274bbedd96ff72644ba).
2025-07-14 01:07:52,041 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:07:52,363 [INFO] Synced 29 slash commands globally.
2025-07-14 01:07:52,363 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:09:01,588 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:09:01,590 [INFO] Loaded cog: embedgen.py
2025-07-14 01:09:01,592 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:09:01,594 [INFO] Loaded cog: giveaway.py
2025-07-14 01:09:01,597 [INFO] Loaded cog: moderation.py
2025-07-14 01:09:01,599 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:09:01,600 [INFO] Loaded cog: rosters.py
2025-07-14 01:09:01,601 [INFO] Loaded cog: shipgame.py
2025-07-14 01:09:01,602 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:09:02,066 [INFO] UEX cache refreshed successfully
2025-07-14 01:09:02,066 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:09:02,068 [INFO] Loaded cog: voice.py
2025-07-14 01:09:02,071 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:09:02,072 [INFO] Loaded cog: welcome.py
2025-07-14 01:09:02,072 [INFO] logging in using static token
2025-07-14 01:09:03,002 [INFO] Shard ID None has connected to Gateway (Session ID: a1fbf9134cb3a26bb4f8c912e6f66a6c).
2025-07-14 01:09:05,024 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:09:05,257 [INFO] Synced 29 slash commands globally.
2025-07-14 01:09:05,257 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:09:49,047 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:09:49,048 [INFO] Loaded cog: embedgen.py
2025-07-14 01:09:49,049 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:09:49,051 [INFO] Loaded cog: giveaway.py
2025-07-14 01:09:49,054 [INFO] Loaded cog: moderation.py
2025-07-14 01:09:49,055 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:09:49,057 [INFO] Loaded cog: rosters.py
2025-07-14 01:09:49,058 [INFO] Loaded cog: shipgame.py
2025-07-14 01:09:49,058 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:09:49,476 [INFO] UEX cache refreshed successfully
2025-07-14 01:09:49,476 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:09:49,478 [INFO] Loaded cog: voice.py
2025-07-14 01:09:49,481 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:09:49,481 [INFO] Loaded cog: welcome.py
2025-07-14 01:09:49,481 [INFO] logging in using static token
2025-07-14 01:09:50,195 [INFO] Shard ID None has connected to Gateway (Session ID: 407fa59029ae2bccbe15df317d164619).
2025-07-14 01:09:52,215 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:09:52,547 [INFO] Synced 29 slash commands globally.
2025-07-14 01:09:52,548 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:19:41,613 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:19:41,615 [INFO] Loaded cog: embedgen.py
2025-07-14 01:19:41,616 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:19:41,618 [INFO] Loaded cog: giveaway.py
2025-07-14 01:19:41,623 [INFO] Loaded cog: moderation.py
2025-07-14 01:19:41,624 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:19:41,625 [INFO] Loaded cog: rosters.py
2025-07-14 01:19:41,627 [INFO] Loaded cog: shipgame.py
2025-07-14 01:19:41,627 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:19:42,071 [INFO] UEX cache refreshed successfully
2025-07-14 01:19:42,071 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:19:42,073 [INFO] Loaded cog: voice.py
2025-07-14 01:19:42,076 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:19:42,077 [INFO] Loaded cog: welcome.py
2025-07-14 01:19:42,077 [INFO] logging in using static token
2025-07-14 01:19:42,905 [INFO] Shard ID None has connected to Gateway (Session ID: 75eecd8ae995439dfcd138a1f8a59f4d).
2025-07-14 01:19:44,912 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:19:45,105 [INFO] Synced 29 slash commands globally.
2025-07-14 01:19:45,105 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:20:41,074 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:20:41,075 [INFO] Loaded cog: embedgen.py
2025-07-14 01:20:41,077 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:20:41,079 [INFO] Loaded cog: giveaway.py
2025-07-14 01:20:41,083 [INFO] Loaded cog: moderation.py
2025-07-14 01:20:41,084 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:20:41,085 [INFO] Loaded cog: rosters.py
2025-07-14 01:20:41,087 [INFO] Loaded cog: shipgame.py
2025-07-14 01:20:41,088 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:20:41,496 [INFO] UEX cache refreshed successfully
2025-07-14 01:20:41,497 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:20:41,498 [INFO] Loaded cog: voice.py
2025-07-14 01:20:41,501 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:20:41,502 [INFO] Loaded cog: welcome.py
2025-07-14 01:20:41,502 [INFO] logging in using static token
2025-07-14 01:20:42,303 [INFO] Shard ID None has connected to Gateway (Session ID: 290cf2dc4885993b1cfa79d03e248dee).
2025-07-14 01:20:44,307 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:20:44,508 [INFO] Synced 29 slash commands globally.
2025-07-14 01:20:44,509 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:20:49,211 [ERROR] Ignoring exception in command 'traderoutes'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\uex_trade.py", line 171, in traderoutes
    await interaction.response.defer()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 661, in defer
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'traderoutes' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-14 01:21:22,924 [ERROR] Error selecting vehicle: 'Message' object has no attribute 'edit_original_response'
2025-07-14 01:21:22,924 [ERROR] Ignoring exception in modal <VehicleSelectionModal timeout=None children=1>:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\uex_trade.py", line 1098, in on_submit
    # Get filtered routes with vehicle SCU consideration
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Message' object has no attribute 'edit_original_response'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ui\modal.py", line 188, in _scheduled_task
    await self.on_submit(interaction)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\uex_trade.py", line 1110, in on_submit
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 774, in send_message
    raise InteractionResponded(self._parent)
discord.errors.InteractionResponded: This interaction has already been responded to before
2025-07-14 01:23:19,177 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:23:19,179 [INFO] Loaded cog: embedgen.py
2025-07-14 01:23:19,180 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:23:19,182 [INFO] Loaded cog: giveaway.py
2025-07-14 01:23:19,185 [INFO] Loaded cog: moderation.py
2025-07-14 01:23:19,186 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:23:19,188 [INFO] Loaded cog: rosters.py
2025-07-14 01:23:19,189 [INFO] Loaded cog: shipgame.py
2025-07-14 01:23:19,191 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:23:19,739 [INFO] UEX cache refreshed successfully
2025-07-14 01:23:19,739 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:23:19,741 [INFO] Loaded cog: voice.py
2025-07-14 01:23:19,744 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:23:19,745 [INFO] Loaded cog: welcome.py
2025-07-14 01:23:19,745 [INFO] logging in using static token
2025-07-14 01:23:20,455 [INFO] Shard ID None has connected to Gateway (Session ID: c0a141fa36961b095c30a176d5d68d78).
2025-07-14 01:23:22,470 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:23:22,752 [INFO] Synced 29 slash commands globally.
2025-07-14 01:23:22,753 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:24:13,905 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:24:13,907 [INFO] Loaded cog: embedgen.py
2025-07-14 01:24:13,908 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:24:13,910 [INFO] Loaded cog: giveaway.py
2025-07-14 01:24:13,913 [INFO] Loaded cog: moderation.py
2025-07-14 01:24:13,915 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:24:13,916 [INFO] Loaded cog: rosters.py
2025-07-14 01:24:13,917 [INFO] Loaded cog: shipgame.py
2025-07-14 01:24:13,918 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:24:14,327 [INFO] UEX cache refreshed successfully
2025-07-14 01:24:14,327 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:24:14,329 [INFO] Loaded cog: voice.py
2025-07-14 01:24:14,332 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:24:14,333 [INFO] Loaded cog: welcome.py
2025-07-14 01:24:14,333 [INFO] logging in using static token
2025-07-14 01:24:15,105 [INFO] Shard ID None has connected to Gateway (Session ID: eb2d19af9ea04a23a93c78a07c84f1f3).
2025-07-14 01:24:17,131 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:24:17,333 [INFO] Synced 29 slash commands globally.
2025-07-14 01:24:17,334 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:24:44,699 [ERROR] Ignoring exception in command 'traderoutes'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\uex_trade.py", line 171, in traderoutes
    await interaction.response.defer()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 661, in defer
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'traderoutes' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-14 01:24:45,181 [ERROR] Error in traderoutes command: unsupported format string passed to NoneType.__format__
2025-07-14 01:26:54,566 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:26:54,567 [INFO] Loaded cog: embedgen.py
2025-07-14 01:26:54,568 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:26:54,570 [INFO] Loaded cog: giveaway.py
2025-07-14 01:26:54,573 [INFO] Loaded cog: moderation.py
2025-07-14 01:26:54,574 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:26:54,576 [INFO] Loaded cog: rosters.py
2025-07-14 01:26:54,578 [INFO] Loaded cog: shipgame.py
2025-07-14 01:26:54,579 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:26:55,024 [INFO] UEX cache refreshed successfully
2025-07-14 01:26:55,025 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:26:55,027 [INFO] Loaded cog: voice.py
2025-07-14 01:26:55,029 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:26:55,030 [INFO] Loaded cog: welcome.py
2025-07-14 01:26:55,030 [INFO] logging in using static token
2025-07-14 01:26:55,810 [INFO] Shard ID None has connected to Gateway (Session ID: 0eedd11daaa27e9b2be49072be4bc9d1).
2025-07-14 01:26:57,821 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:26:58,089 [INFO] Synced 29 slash commands globally.
2025-07-14 01:26:58,089 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:27:56,816 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:27:56,817 [INFO] Loaded cog: embedgen.py
2025-07-14 01:27:56,819 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:27:56,821 [INFO] Loaded cog: giveaway.py
2025-07-14 01:27:56,824 [INFO] Loaded cog: moderation.py
2025-07-14 01:27:56,825 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:27:56,827 [INFO] Loaded cog: rosters.py
2025-07-14 01:27:56,828 [INFO] Loaded cog: shipgame.py
2025-07-14 01:27:56,829 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:27:57,249 [INFO] UEX cache refreshed successfully
2025-07-14 01:27:57,250 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:27:57,251 [INFO] Loaded cog: voice.py
2025-07-14 01:27:57,254 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:27:57,255 [INFO] Loaded cog: welcome.py
2025-07-14 01:27:57,255 [INFO] logging in using static token
2025-07-14 01:27:57,896 [INFO] Shard ID None has connected to Gateway (Session ID: ea06699509eacf6f2966e8c63a2d5562).
2025-07-14 01:27:59,936 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:28:00,207 [INFO] Synced 29 slash commands globally.
2025-07-14 01:28:00,207 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:30:00,136 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:30:00,137 [INFO] Loaded cog: embedgen.py
2025-07-14 01:30:00,139 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:30:00,141 [INFO] Loaded cog: giveaway.py
2025-07-14 01:30:00,144 [INFO] Loaded cog: moderation.py
2025-07-14 01:30:00,145 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:30:00,147 [INFO] Loaded cog: rosters.py
2025-07-14 01:30:00,148 [INFO] Loaded cog: shipgame.py
2025-07-14 01:30:00,149 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:30:00,597 [INFO] UEX cache refreshed successfully
2025-07-14 01:30:00,598 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:30:00,599 [INFO] Loaded cog: voice.py
2025-07-14 01:30:00,603 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:30:00,603 [INFO] Loaded cog: welcome.py
2025-07-14 01:30:00,604 [INFO] logging in using static token
2025-07-14 01:30:01,623 [INFO] Shard ID None has connected to Gateway (Session ID: 8e5f84b7f3e03c8190db71fef6648c1e).
2025-07-14 01:30:03,623 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:30:03,879 [INFO] Synced 29 slash commands globally.
2025-07-14 01:30:03,880 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:30:12,573 [WARNING] Interaction expired while processing traderoutes command
2025-07-14 01:30:32,734 [ERROR] Error selecting vehicle: 'Message' object has no attribute 'edit_original_response'
2025-07-14 01:34:15,026 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:34:15,028 [INFO] Loaded cog: embedgen.py
2025-07-14 01:34:15,029 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:34:15,031 [INFO] Loaded cog: giveaway.py
2025-07-14 01:34:15,035 [INFO] Loaded cog: moderation.py
2025-07-14 01:34:15,036 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:34:15,037 [INFO] Loaded cog: rosters.py
2025-07-14 01:34:15,038 [INFO] Loaded cog: shipgame.py
2025-07-14 01:34:15,039 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:34:15,480 [INFO] UEX cache refreshed successfully
2025-07-14 01:34:15,480 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:34:15,482 [INFO] Loaded cog: voice.py
2025-07-14 01:34:15,485 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:34:15,486 [INFO] Loaded cog: welcome.py
2025-07-14 01:34:15,487 [INFO] logging in using static token
2025-07-14 01:34:16,236 [INFO] Shard ID None has connected to Gateway (Session ID: acf192732a4bcc83663a321a2d5a191e).
2025-07-14 01:34:18,258 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:34:18,667 [INFO] Synced 29 slash commands globally.
2025-07-14 01:34:18,667 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:35:16,714 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:35:16,715 [INFO] Loaded cog: embedgen.py
2025-07-14 01:35:16,717 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:35:16,719 [INFO] Loaded cog: giveaway.py
2025-07-14 01:35:16,723 [INFO] Loaded cog: moderation.py
2025-07-14 01:35:16,724 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:35:16,725 [INFO] Loaded cog: rosters.py
2025-07-14 01:35:16,726 [INFO] Loaded cog: shipgame.py
2025-07-14 01:35:16,727 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:35:17,174 [INFO] UEX cache refreshed successfully
2025-07-14 01:35:17,174 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:35:17,176 [INFO] Loaded cog: voice.py
2025-07-14 01:35:17,179 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:35:17,179 [INFO] Loaded cog: welcome.py
2025-07-14 01:35:17,179 [INFO] logging in using static token
2025-07-14 01:35:17,912 [INFO] Shard ID None has connected to Gateway (Session ID: 30c0781becca357ea52ed5ddaa16d5b6).
2025-07-14 01:35:19,946 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:35:20,314 [INFO] Synced 29 slash commands globally.
2025-07-14 01:35:20,314 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:37:43,370 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:37:43,371 [INFO] Loaded cog: embedgen.py
2025-07-14 01:37:43,372 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:37:43,374 [INFO] Loaded cog: giveaway.py
2025-07-14 01:37:43,377 [INFO] Loaded cog: moderation.py
2025-07-14 01:37:43,379 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:37:43,380 [INFO] Loaded cog: rosters.py
2025-07-14 01:37:43,381 [INFO] Loaded cog: shipgame.py
2025-07-14 01:37:43,382 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:37:43,792 [INFO] UEX cache refreshed successfully
2025-07-14 01:37:43,792 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:37:43,793 [INFO] Loaded cog: voice.py
2025-07-14 01:37:43,797 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:37:43,797 [INFO] Loaded cog: welcome.py
2025-07-14 01:37:43,797 [INFO] logging in using static token
2025-07-14 01:37:45,888 [INFO] Shard ID None has connected to Gateway (Session ID: 64ed8789505f62467531700f7bd81039).
2025-07-14 01:37:47,894 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:37:48,075 [INFO] Synced 29 slash commands globally.
2025-07-14 01:37:48,075 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:37:53,994 [WARNING] Interaction expired while processing traderoutes command
2025-07-14 01:42:08,971 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:42:08,973 [INFO] Loaded cog: embedgen.py
2025-07-14 01:42:08,974 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:42:08,976 [INFO] Loaded cog: giveaway.py
2025-07-14 01:42:08,980 [INFO] Loaded cog: moderation.py
2025-07-14 01:42:08,981 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:42:08,983 [INFO] Loaded cog: rosters.py
2025-07-14 01:42:08,984 [INFO] Loaded cog: shipgame.py
2025-07-14 01:42:08,985 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:42:09,393 [INFO] UEX cache refreshed successfully
2025-07-14 01:42:09,393 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:42:09,394 [INFO] Loaded cog: voice.py
2025-07-14 01:42:09,398 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:42:09,398 [INFO] Loaded cog: welcome.py
2025-07-14 01:42:09,398 [INFO] logging in using static token
2025-07-14 01:42:10,093 [INFO] Shard ID None has connected to Gateway (Session ID: ce587c82e7412f1a3981c1dc76e91c67).
2025-07-14 01:42:12,098 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:42:12,385 [INFO] Synced 29 slash commands globally.
2025-07-14 01:42:12,386 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:42:48,591 [ERROR] Ignoring exception in command 'commodities'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\uex_trade.py", line 299, in commodities
    scu_amount = max(scu_amount or 0, 0)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 661, in defer
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'commodities' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-14 01:45:40,177 [INFO] UEX cache refreshed successfully
2025-07-14 01:46:01,473 [ERROR] Ignoring exception in command 'price'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\uex_trade.py", line 406, in price
    await interaction.response.defer()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 661, in defer
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'price' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-14 01:47:59,383 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:47:59,385 [INFO] Loaded cog: embedgen.py
2025-07-14 01:47:59,387 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:47:59,389 [INFO] Loaded cog: giveaway.py
2025-07-14 01:47:59,392 [INFO] Loaded cog: moderation.py
2025-07-14 01:47:59,394 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:47:59,395 [INFO] Loaded cog: rosters.py
2025-07-14 01:47:59,397 [INFO] Loaded cog: shipgame.py
2025-07-14 01:47:59,398 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:47:59,850 [INFO] UEX cache refreshed successfully
2025-07-14 01:47:59,850 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:47:59,851 [INFO] Loaded cog: voice.py
2025-07-14 01:47:59,855 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:47:59,855 [INFO] Loaded cog: welcome.py
2025-07-14 01:47:59,855 [INFO] logging in using static token
2025-07-14 01:48:00,831 [INFO] Shard ID None has connected to Gateway (Session ID: 7e515826ad966bfda95890fa639c7d35).
2025-07-14 01:48:02,843 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:48:03,103 [INFO] Synced 29 slash commands globally.
2025-07-14 01:48:03,103 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:59:19,054 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:59:19,056 [INFO] Loaded cog: embedgen.py
2025-07-14 01:59:19,057 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:59:19,060 [INFO] Loaded cog: giveaway.py
2025-07-14 01:59:19,063 [INFO] Loaded cog: moderation.py
2025-07-14 01:59:19,064 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:59:19,067 [INFO] Loaded cog: rosters.py
2025-07-14 01:59:19,068 [INFO] Loaded cog: shipgame.py
2025-07-14 01:59:19,069 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:59:30,285 [INFO] UEX cache refreshed successfully
2025-07-14 01:59:30,285 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:59:30,287 [INFO] Loaded cog: voice.py
2025-07-14 01:59:30,291 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:59:30,291 [INFO] Loaded cog: welcome.py
2025-07-14 01:59:30,291 [INFO] logging in using static token
2025-07-14 01:59:31,132 [INFO] Shard ID None has connected to Gateway (Session ID: 40bd998654e752cdeef2bae3c800b0e7).
2025-07-14 01:59:33,154 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:59:33,344 [INFO] Synced 29 slash commands globally.
2025-07-14 01:59:33,345 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 02:00:09,113 [INFO] Loaded cog: applytoaegis.py
2025-07-14 02:00:09,115 [INFO] Loaded cog: embedgen.py
2025-07-14 02:00:09,118 [INFO] Loaded cog: fleetdata.py
2025-07-14 02:00:09,121 [INFO] Loaded cog: giveaway.py
2025-07-14 02:00:09,125 [INFO] Loaded cog: moderation.py
2025-07-14 02:00:09,127 [INFO] Loaded cog: rolerequest.py
2025-07-14 02:00:09,128 [INFO] Loaded cog: rosters.py
2025-07-14 02:00:09,129 [INFO] Loaded cog: shipgame.py
2025-07-14 02:00:09,130 [INFO] Loaded cog: storagetracker.py
2025-07-14 02:00:20,795 [INFO] UEX cache refreshed successfully
2025-07-14 02:00:20,795 [INFO] Loaded cog: uex_trade.py
2025-07-14 02:00:20,797 [INFO] Loaded cog: voice.py
2025-07-14 02:00:20,799 [INFO] Loaded cog: voicetracker.py
2025-07-14 02:00:20,800 [INFO] Loaded cog: welcome.py
2025-07-14 02:00:20,800 [INFO] logging in using static token
2025-07-14 02:00:21,783 [INFO] Shard ID None has connected to Gateway (Session ID: 7afac0b0f37b055adfce24feb0b12bb3).
2025-07-14 02:00:23,802 [INFO] Logged in as Aegis Nox Bot
2025-07-14 02:00:24,119 [INFO] Synced 29 slash commands globally.
2025-07-14 02:00:24,130 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 02:00:35,213 [ERROR] Ignoring exception in command 'commodities'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\uex_trade.py", line 402, in commodities
    await interaction.response.defer()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 661, in defer
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'commodities' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-14 02:00:43,426 [ERROR] Ignoring exception in command 'price'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\uex_trade.py", line 492, in price
    await interaction.response.defer()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 661, in defer
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'price' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-14 02:01:40,087 [WARNING] Interaction expired while processing traderoutes command
2025-07-14 02:01:51,471 [WARNING] Interaction expired while processing traderoutes command
2025-07-14 02:06:09,457 [INFO] Loaded cog: applytoaegis.py
2025-07-14 02:06:09,459 [INFO] Loaded cog: embedgen.py
2025-07-14 02:06:09,460 [INFO] Loaded cog: fleetdata.py
2025-07-14 02:06:09,463 [INFO] Loaded cog: giveaway.py
2025-07-14 02:06:09,466 [INFO] Loaded cog: moderation.py
2025-07-14 02:06:09,468 [INFO] Loaded cog: rolerequest.py
2025-07-14 02:06:09,469 [INFO] Loaded cog: rosters.py
2025-07-14 02:06:09,470 [INFO] Loaded cog: shipgame.py
2025-07-14 02:06:09,471 [INFO] Loaded cog: storagetracker.py
2025-07-14 02:06:20,846 [INFO] UEX cache refreshed successfully
2025-07-14 02:06:20,846 [INFO] Loaded cog: uex_trade.py
2025-07-14 02:06:20,848 [INFO] Loaded cog: voice.py
2025-07-14 02:06:20,852 [INFO] Loaded cog: voicetracker.py
2025-07-14 02:06:20,852 [INFO] Loaded cog: welcome.py
2025-07-14 02:06:20,853 [INFO] logging in using static token
2025-07-14 02:06:22,009 [INFO] Shard ID None has connected to Gateway (Session ID: d977a15bec3738dcbc2b60dce240446a).
2025-07-14 02:06:24,022 [INFO] Logged in as Aegis Nox Bot
2025-07-14 02:06:24,182 [INFO] Synced 29 slash commands globally.
2025-07-14 02:06:24,182 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 02:16:56,389 [INFO] Loaded cog: applytoaegis.py
2025-07-14 02:16:56,391 [INFO] Loaded cog: embedgen.py
2025-07-14 02:16:56,392 [INFO] Loaded cog: fleetdata.py
2025-07-14 02:16:56,395 [INFO] Loaded cog: giveaway.py
2025-07-14 02:16:56,399 [INFO] Loaded cog: moderation.py
2025-07-14 02:16:56,401 [INFO] Loaded cog: rolerequest.py
2025-07-14 02:16:56,403 [INFO] Loaded cog: rosters.py
2025-07-14 02:16:56,404 [INFO] Loaded cog: shipgame.py
2025-07-14 02:16:56,405 [INFO] Loaded cog: storagetracker.py
2025-07-14 02:17:07,961 [INFO] UEX cache refreshed successfully
2025-07-14 02:17:07,961 [INFO] Loaded cog: uex_trade.py
2025-07-14 02:17:07,963 [INFO] Loaded cog: voice.py
2025-07-14 02:17:07,966 [INFO] Loaded cog: voicetracker.py
2025-07-14 02:17:07,967 [INFO] Loaded cog: welcome.py
2025-07-14 02:17:07,967 [INFO] logging in using static token
2025-07-14 02:17:08,758 [INFO] Shard ID None has connected to Gateway (Session ID: b05de5f7a7b1585103bbe107b0eed79e).
2025-07-14 02:17:10,782 [INFO] Logged in as Aegis Nox Bot
2025-07-14 02:17:11,021 [INFO] Synced 29 slash commands globally.
2025-07-14 02:17:11,021 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 02:17:20,749 [INFO] Found 74 tradeable commodities
2025-07-14 02:17:20,749 [INFO] Commodity prices cache has 169 entries
2025-07-14 02:17:20,750 [INFO] Generated 0 total routes
2025-07-14 02:19:51,077 [INFO] Loaded cog: applytoaegis.py
2025-07-14 02:19:51,078 [INFO] Loaded cog: embedgen.py
2025-07-14 02:19:51,079 [INFO] Loaded cog: fleetdata.py
2025-07-14 02:19:51,082 [INFO] Loaded cog: giveaway.py
2025-07-14 02:19:51,085 [INFO] Loaded cog: moderation.py
2025-07-14 02:19:51,086 [INFO] Loaded cog: rolerequest.py
2025-07-14 02:19:51,088 [INFO] Loaded cog: rosters.py
2025-07-14 02:19:51,089 [INFO] Loaded cog: shipgame.py
2025-07-14 02:19:51,090 [INFO] Loaded cog: storagetracker.py
2025-07-14 02:20:02,441 [INFO] UEX cache refreshed successfully
2025-07-14 02:20:02,441 [INFO] Loaded cog: uex_trade.py
2025-07-14 02:20:02,443 [INFO] Loaded cog: voice.py
2025-07-14 02:20:02,446 [INFO] Loaded cog: voicetracker.py
2025-07-14 02:20:02,447 [INFO] Loaded cog: welcome.py
2025-07-14 02:20:02,447 [INFO] logging in using static token
2025-07-14 02:20:03,117 [INFO] Shard ID None has connected to Gateway (Session ID: 40cf83c26920e5568b3a642f68fa5a1b).
2025-07-14 02:20:05,128 [INFO] Logged in as Aegis Nox Bot
2025-07-14 02:20:05,348 [INFO] Synced 29 slash commands globally.
2025-07-14 02:20:05,348 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 02:20:31,273 [INFO] Loaded cog: applytoaegis.py
2025-07-14 02:20:31,275 [INFO] Loaded cog: embedgen.py
2025-07-14 02:20:31,276 [INFO] Loaded cog: fleetdata.py
2025-07-14 02:20:31,279 [INFO] Loaded cog: giveaway.py
2025-07-14 02:20:31,282 [INFO] Loaded cog: moderation.py
2025-07-14 02:20:31,283 [INFO] Loaded cog: rolerequest.py
2025-07-14 02:20:31,284 [INFO] Loaded cog: rosters.py
2025-07-14 02:20:31,285 [INFO] Loaded cog: shipgame.py
2025-07-14 02:20:31,286 [INFO] Loaded cog: storagetracker.py
2025-07-14 02:20:42,615 [INFO] UEX cache refreshed successfully
2025-07-14 02:20:42,615 [INFO] Loaded cog: uex_trade.py
2025-07-14 02:20:42,617 [INFO] Loaded cog: voice.py
2025-07-14 02:20:42,621 [INFO] Loaded cog: voicetracker.py
2025-07-14 02:20:42,622 [INFO] Loaded cog: welcome.py
2025-07-14 02:20:42,622 [INFO] logging in using static token
2025-07-14 02:20:43,344 [INFO] Shard ID None has connected to Gateway (Session ID: 791404552d462b3da20ef9aaf37bb78b).
2025-07-14 02:20:45,367 [INFO] Logged in as Aegis Nox Bot
2025-07-14 02:20:45,616 [INFO] Synced 29 slash commands globally.
2025-07-14 02:20:45,616 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 02:22:37,962 [INFO] Loaded cog: applytoaegis.py
2025-07-14 02:22:37,964 [INFO] Loaded cog: embedgen.py
2025-07-14 02:22:37,965 [INFO] Loaded cog: fleetdata.py
2025-07-14 02:22:37,967 [INFO] Loaded cog: giveaway.py
2025-07-14 02:22:37,971 [INFO] Loaded cog: moderation.py
2025-07-14 02:22:37,973 [INFO] Loaded cog: rolerequest.py
2025-07-14 02:22:37,974 [INFO] Loaded cog: rosters.py
2025-07-14 02:22:37,976 [INFO] Loaded cog: shipgame.py
2025-07-14 02:22:37,977 [INFO] Loaded cog: storagetracker.py
2025-07-14 02:22:49,216 [INFO] UEX cache refreshed successfully
2025-07-14 02:22:49,216 [INFO] Loaded cog: uex_trade.py
2025-07-14 02:22:49,218 [INFO] Loaded cog: voice.py
2025-07-14 02:22:49,222 [INFO] Loaded cog: voicetracker.py
2025-07-14 02:22:49,222 [INFO] Loaded cog: welcome.py
2025-07-14 02:22:49,223 [INFO] logging in using static token
2025-07-14 02:22:50,045 [INFO] Shard ID None has connected to Gateway (Session ID: 6392399249447c00ddb39cc2c5674c69).
2025-07-14 02:22:52,068 [INFO] Logged in as Aegis Nox Bot
2025-07-14 02:22:52,293 [INFO] Synced 29 slash commands globally.
2025-07-14 02:22:52,294 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 02:24:49,666 [INFO] Loaded cog: applytoaegis.py
2025-07-14 02:24:49,668 [INFO] Loaded cog: embedgen.py
2025-07-14 02:24:49,672 [INFO] Loaded cog: fleetdata.py
2025-07-14 02:24:49,678 [INFO] Loaded cog: giveaway.py
2025-07-14 02:24:49,683 [INFO] Loaded cog: moderation.py
2025-07-14 02:24:49,684 [INFO] Loaded cog: rolerequest.py
2025-07-14 02:24:49,686 [INFO] Loaded cog: rosters.py
2025-07-14 02:24:49,688 [INFO] Loaded cog: shipgame.py
2025-07-14 02:24:49,690 [INFO] Loaded cog: storagetracker.py
2025-07-14 02:25:00,763 [INFO] UEX cache refreshed successfully
2025-07-14 02:25:00,763 [INFO] Loaded cog: uex_trade.py
2025-07-14 02:25:00,765 [INFO] Loaded cog: voice.py
2025-07-14 02:25:00,767 [INFO] Loaded cog: voicetracker.py
2025-07-14 02:25:00,768 [INFO] Loaded cog: welcome.py
2025-07-14 02:25:00,768 [INFO] logging in using static token
2025-07-14 02:25:01,484 [INFO] Shard ID None has connected to Gateway (Session ID: 3c1101029a7015aa587a8bccec82106a).
2025-07-14 02:25:03,507 [INFO] Logged in as Aegis Nox Bot
2025-07-14 02:25:03,700 [INFO] Synced 29 slash commands globally.
2025-07-14 02:25:03,700 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 02:25:54,032 [INFO] Loaded cog: applytoaegis.py
2025-07-14 02:25:54,033 [INFO] Loaded cog: embedgen.py
2025-07-14 02:25:54,034 [INFO] Loaded cog: fleetdata.py
2025-07-14 02:25:54,037 [INFO] Loaded cog: giveaway.py
2025-07-14 02:25:54,041 [INFO] Loaded cog: moderation.py
2025-07-14 02:25:54,043 [INFO] Loaded cog: rolerequest.py
2025-07-14 02:25:54,044 [INFO] Loaded cog: rosters.py
2025-07-14 02:25:54,045 [INFO] Loaded cog: shipgame.py
2025-07-14 02:25:54,046 [INFO] Loaded cog: storagetracker.py
2025-07-14 02:26:05,249 [INFO] UEX cache refreshed successfully
2025-07-14 02:26:05,249 [INFO] Loaded cog: uex_trade.py
2025-07-14 02:26:05,251 [INFO] Loaded cog: voice.py
2025-07-14 02:26:05,254 [INFO] Loaded cog: voicetracker.py
2025-07-14 02:26:05,254 [INFO] Loaded cog: welcome.py
2025-07-14 02:26:05,254 [INFO] logging in using static token
2025-07-14 02:26:05,860 [INFO] Shard ID None has connected to Gateway (Session ID: 2b10e9fdb2e7a6dc52ad981cce88a22c).
2025-07-14 02:26:07,855 [INFO] Logged in as Aegis Nox Bot
2025-07-14 02:26:08,047 [INFO] Synced 29 slash commands globally.
2025-07-14 02:26:08,047 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 02:27:20,009 [INFO] Loaded cog: applytoaegis.py
2025-07-14 02:27:20,011 [INFO] Loaded cog: embedgen.py
2025-07-14 02:27:20,012 [INFO] Loaded cog: fleetdata.py
2025-07-14 02:27:20,014 [INFO] Loaded cog: giveaway.py
2025-07-14 02:27:20,018 [INFO] Loaded cog: moderation.py
2025-07-14 02:27:20,020 [INFO] Loaded cog: rolerequest.py
2025-07-14 02:27:20,022 [INFO] Loaded cog: rosters.py
2025-07-14 02:27:20,023 [INFO] Loaded cog: shipgame.py
2025-07-14 02:27:20,024 [INFO] Loaded cog: storagetracker.py
2025-07-14 02:27:31,043 [INFO] UEX cache refreshed successfully
2025-07-14 02:27:31,043 [INFO] Loaded cog: uex_trade.py
2025-07-14 02:27:31,045 [INFO] Loaded cog: voice.py
2025-07-14 02:27:31,053 [INFO] Loaded cog: voicetracker.py
2025-07-14 02:27:31,053 [INFO] Loaded cog: welcome.py
2025-07-14 02:27:31,054 [INFO] logging in using static token
2025-07-14 02:27:31,948 [INFO] Shard ID None has connected to Gateway (Session ID: 2434fb740382c33d723704816dbc0c48).
2025-07-14 02:27:33,966 [INFO] Logged in as Aegis Nox Bot
2025-07-14 02:27:34,242 [INFO] Synced 29 slash commands globally.
2025-07-14 02:27:34,242 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 02:27:48,684 [INFO] Vehicle autocomplete called with: ''
2025-07-14 02:27:48,684 [INFO] Found 0 vehicles with cargo capacity
2025-07-14 02:27:48,684 [INFO] Returning 0 autocomplete options
2025-07-14 02:27:49,971 [INFO] Vehicle autocomplete called with: '8'
2025-07-14 02:27:49,971 [INFO] Found 0 vehicles with cargo capacity
2025-07-14 02:27:49,971 [INFO] Returning 0 autocomplete options
2025-07-14 02:27:50,838 [INFO] Vehicle autocomplete called with: '89'
2025-07-14 02:27:50,839 [INFO] Found 0 vehicles with cargo capacity
2025-07-14 02:27:50,839 [INFO] Returning 0 autocomplete options
2025-07-14 02:27:51,530 [INFO] Vehicle autocomplete called with: '890'
2025-07-14 02:27:51,531 [INFO] Found 0 vehicles with cargo capacity
2025-07-14 02:27:51,531 [INFO] Returning 0 autocomplete options
2025-07-14 02:27:55,365 [INFO] Vehicle autocomplete called with: '890 '
2025-07-14 02:27:55,365 [INFO] Found 0 vehicles with cargo capacity
2025-07-14 02:27:55,365 [INFO] Returning 0 autocomplete options
2025-07-14 02:27:56,290 [INFO] Vehicle autocomplete called with: '890 J'
2025-07-14 02:27:56,290 [INFO] Found 0 vehicles with cargo capacity
2025-07-14 02:27:56,290 [INFO] Returning 0 autocomplete options
2025-07-14 02:27:57,480 [INFO] Vehicle autocomplete called with: '890 Jump'
2025-07-14 02:27:57,481 [INFO] Found 0 vehicles with cargo capacity
2025-07-14 02:27:57,481 [INFO] Returning 0 autocomplete options
2025-07-14 02:29:05,001 [INFO] Loaded cog: applytoaegis.py
2025-07-14 02:29:05,002 [INFO] Loaded cog: embedgen.py
2025-07-14 02:29:05,004 [INFO] Loaded cog: fleetdata.py
2025-07-14 02:29:05,007 [INFO] Loaded cog: giveaway.py
2025-07-14 02:29:05,009 [INFO] Loaded cog: moderation.py
2025-07-14 02:29:05,011 [INFO] Loaded cog: rolerequest.py
2025-07-14 02:29:05,012 [INFO] Loaded cog: rosters.py
2025-07-14 02:29:05,013 [INFO] Loaded cog: shipgame.py
2025-07-14 02:29:05,014 [INFO] Loaded cog: storagetracker.py
2025-07-14 02:29:16,095 [INFO] UEX cache refreshed successfully
2025-07-14 02:29:16,095 [INFO] Loaded cog: uex_trade.py
2025-07-14 02:29:16,097 [INFO] Loaded cog: voice.py
2025-07-14 02:29:16,101 [INFO] Loaded cog: voicetracker.py
2025-07-14 02:29:16,101 [INFO] Loaded cog: welcome.py
2025-07-14 02:29:16,101 [INFO] logging in using static token
2025-07-14 02:29:16,954 [INFO] Shard ID None has connected to Gateway (Session ID: 78d87cc8912dbb086842d5cb33d5cc27).
2025-07-14 02:29:18,963 [INFO] Logged in as Aegis Nox Bot
2025-07-14 02:29:19,404 [INFO] Synced 29 slash commands globally.
2025-07-14 02:29:19,404 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 02:29:49,620 [INFO] Vehicle autocomplete called with: '890 Jum'
2025-07-14 02:29:49,620 [INFO] Found 0 vehicles with cargo capacity
2025-07-14 02:29:49,620 [INFO] Returning 0 autocomplete options
2025-07-14 02:29:49,622 [INFO] Vehicle autocomplete called with: '890 Jum'
2025-07-14 02:29:49,622 [INFO] Sample vehicle keys: ['id', 'id_company', 'id_parent', 'ids_vehicles_loaners', 'name', 'name_full', 'slug', 'uuid', 'scu', 'crew', 'mass', 'width', 'height', 'length', 'fuel_quantum', 'fuel_hydrogen', 'container_sizes', 'is_addon', 'is_boarding', 'is_bomber', 'is_cargo', 'is_carrier', 'is_civilian', 'is_concept', 'is_construction', 'is_datarunner', 'is_docking', 'is_emp', 'is_exploration', 'is_ground_vehicle', 'is_hangar', 'is_industrial', 'is_interdiction', 'is_loading_dock', 'is_medical', 'is_military', 'is_mining', 'is_passenger', 'is_qed', 'is_racing', 'is_refinery', 'is_refuel', 'is_repair', 'is_research', 'is_salvage', 'is_scanning', 'is_science', 'is_showdown_winner', 'is_spaceship', 'is_starter', 'is_stealth', 'is_tractor_beam', 'is_quantum_capable', 'url_photo', 'url_store', 'url_brochure', 'url_hotsite', 'url_video', 'url_photos', 'pad_type', 'game_version', 'date_added', 'date_modified', 'company_name']
2025-07-14 02:29:49,623 [INFO] Sample vehicle: {'id': 1, 'id_company': 195, 'id_parent': 1, 'ids_vehicles_loaners': '', 'name': '100i', 'name_full': 'Origin 100i', 'slug': '100i', 'uuid': '6135a874-4cb1-4f49-9f29-5781e5991f2b', 'scu': 2, 'crew': '1', 'mass': 0, 'width': 12, 'height': 5, 'length': 19, 'fuel_quantum': 0, 'fuel_hydrogen': 0, 'container_sizes': '1,2', 'is_addon': 0, 'is_boarding': 0, 'is_bomber': 0, 'is_cargo': 0, 'is_carrier': 0, 'is_civilian': 1, 'is_concept': 0, 'is_construction': 0, 'is_datarunner': 0, 'is_docking': 0, 'is_emp': 0, 'is_exploration': 0, 'is_ground_vehicle': 0, 'is_hangar': 0, 'is_industrial': 0, 'is_interdiction': 0, 'is_loading_dock': 0, 'is_medical': 0, 'is_military': 0, 'is_mining': 0, 'is_passenger': 0, 'is_qed': 0, 'is_racing': 0, 'is_refinery': 0, 'is_refuel': 0, 'is_repair': 0, 'is_research': 0, 'is_salvage': 0, 'is_scanning': 0, 'is_science': 0, 'is_showdown_winner': 0, 'is_spaceship': 1, 'is_starter': 1, 'is_stealth': 0, 'is_tractor_beam': 0, 'is_quantum_capable': 1, 'url_photo': 'https://assets.uexcorp.space/img/vehicles/1/images/fc9508537140e21f7959e16f152c915cc8d32dc2.jpg', 'url_store': 'https://robertsspaceindustries.com/pledge/ships/origin-100/100i', 'url_brochure': 'https://robertsspaceindustries.com/media/sb217acbbj976r/source/Origin-100-Series-Brochure-FINAL.pdf', 'url_hotsite': 'https://robertsspaceindustries.com/comm-link//16505-Introducing-The-Origin-100-Series', 'url_video': 'https://www.youtube.com/watch?v=QGMxGdzYVY8', 'url_photos': '["https:\\/\\/media.robertsspaceindustries.com\\/cjnxt0sdt9m1z\\/store_slideshow_large.png","https:\\/\\/media.robertsspaceindustries.com\\/ikj3i9042ppst\\/store_slideshow_large.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/dl3bq4ot40edwr\\/store_slideshow_large\\/SKUs_100i_Starter_PLEDGE.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/bapnpk9usqxxhr\\/store_slideshow_large\\/100i.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/7ufjldd2bh6tir\\/store_slideshow_large\\/ORIG_100_Sketch_PJ01_MA_Will_FINAL-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/5vr9snbkr4ktdr\\/store_slideshow_large\\/Origin_100_Promo_Closeup_Front_Cover_PJ01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/faqfpklm68itvr\\/store_slideshow_large\\/Origin_100_Promo_Closeup_Back_Cover_PJ01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/gpiycmt5scluhr\\/store_slideshow_large\\/ORIG_100i_Interior_design_Shot_AL03_CC-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/41fid4n8h94a3r\\/store_slideshow_large\\/Origin_100_Promo_Closeup_Man_Vent_PJ02_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/442bjw94xsk8br\\/store_slideshow_large\\/Origin_100_Promo_Logo_Pj01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/jhifpuskfsm4gr\\/store_slideshow_large\\/Origin_100_All_100s_LineUP_Front_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/5yuztn7qxhn11r\\/store_slideshow_large\\/Origin_100_Promo_All100s_LineUp_Rear_PJ01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/an5wwm0nihyu0r\\/store_slideshow_large\\/Origin_100_Promo_Exploration_Promo_shot_PJ05-Min.jpg"]', 'pad_type': 'XS', 'game_version': '4.0', 'date_added': 1703505653, 'date_modified': 1741629801, 'company_name': 'Origin Jumpworks'}
2025-07-14 02:29:49,623 [INFO] Trying alternative cargo fields, found 135 vehicles
2025-07-14 02:29:49,623 [INFO] Found 135 vehicles with cargo capacity
2025-07-14 02:29:49,624 [INFO] Returning 1 autocomplete options
2025-07-14 02:30:04,781 [INFO] Vehicle autocomplete called with: ''
2025-07-14 02:30:04,781 [INFO] Sample vehicle keys: ['id', 'id_company', 'id_parent', 'ids_vehicles_loaners', 'name', 'name_full', 'slug', 'uuid', 'scu', 'crew', 'mass', 'width', 'height', 'length', 'fuel_quantum', 'fuel_hydrogen', 'container_sizes', 'is_addon', 'is_boarding', 'is_bomber', 'is_cargo', 'is_carrier', 'is_civilian', 'is_concept', 'is_construction', 'is_datarunner', 'is_docking', 'is_emp', 'is_exploration', 'is_ground_vehicle', 'is_hangar', 'is_industrial', 'is_interdiction', 'is_loading_dock', 'is_medical', 'is_military', 'is_mining', 'is_passenger', 'is_qed', 'is_racing', 'is_refinery', 'is_refuel', 'is_repair', 'is_research', 'is_salvage', 'is_scanning', 'is_science', 'is_showdown_winner', 'is_spaceship', 'is_starter', 'is_stealth', 'is_tractor_beam', 'is_quantum_capable', 'url_photo', 'url_store', 'url_brochure', 'url_hotsite', 'url_video', 'url_photos', 'pad_type', 'game_version', 'date_added', 'date_modified', 'company_name']
2025-07-14 02:30:04,781 [INFO] Sample vehicle: {'id': 1, 'id_company': 195, 'id_parent': 1, 'ids_vehicles_loaners': '', 'name': '100i', 'name_full': 'Origin 100i', 'slug': '100i', 'uuid': '6135a874-4cb1-4f49-9f29-5781e5991f2b', 'scu': 2, 'crew': '1', 'mass': 0, 'width': 12, 'height': 5, 'length': 19, 'fuel_quantum': 0, 'fuel_hydrogen': 0, 'container_sizes': '1,2', 'is_addon': 0, 'is_boarding': 0, 'is_bomber': 0, 'is_cargo': 0, 'is_carrier': 0, 'is_civilian': 1, 'is_concept': 0, 'is_construction': 0, 'is_datarunner': 0, 'is_docking': 0, 'is_emp': 0, 'is_exploration': 0, 'is_ground_vehicle': 0, 'is_hangar': 0, 'is_industrial': 0, 'is_interdiction': 0, 'is_loading_dock': 0, 'is_medical': 0, 'is_military': 0, 'is_mining': 0, 'is_passenger': 0, 'is_qed': 0, 'is_racing': 0, 'is_refinery': 0, 'is_refuel': 0, 'is_repair': 0, 'is_research': 0, 'is_salvage': 0, 'is_scanning': 0, 'is_science': 0, 'is_showdown_winner': 0, 'is_spaceship': 1, 'is_starter': 1, 'is_stealth': 0, 'is_tractor_beam': 0, 'is_quantum_capable': 1, 'url_photo': 'https://assets.uexcorp.space/img/vehicles/1/images/fc9508537140e21f7959e16f152c915cc8d32dc2.jpg', 'url_store': 'https://robertsspaceindustries.com/pledge/ships/origin-100/100i', 'url_brochure': 'https://robertsspaceindustries.com/media/sb217acbbj976r/source/Origin-100-Series-Brochure-FINAL.pdf', 'url_hotsite': 'https://robertsspaceindustries.com/comm-link//16505-Introducing-The-Origin-100-Series', 'url_video': 'https://www.youtube.com/watch?v=QGMxGdzYVY8', 'url_photos': '["https:\\/\\/media.robertsspaceindustries.com\\/cjnxt0sdt9m1z\\/store_slideshow_large.png","https:\\/\\/media.robertsspaceindustries.com\\/ikj3i9042ppst\\/store_slideshow_large.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/dl3bq4ot40edwr\\/store_slideshow_large\\/SKUs_100i_Starter_PLEDGE.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/bapnpk9usqxxhr\\/store_slideshow_large\\/100i.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/7ufjldd2bh6tir\\/store_slideshow_large\\/ORIG_100_Sketch_PJ01_MA_Will_FINAL-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/5vr9snbkr4ktdr\\/store_slideshow_large\\/Origin_100_Promo_Closeup_Front_Cover_PJ01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/faqfpklm68itvr\\/store_slideshow_large\\/Origin_100_Promo_Closeup_Back_Cover_PJ01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/gpiycmt5scluhr\\/store_slideshow_large\\/ORIG_100i_Interior_design_Shot_AL03_CC-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/41fid4n8h94a3r\\/store_slideshow_large\\/Origin_100_Promo_Closeup_Man_Vent_PJ02_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/442bjw94xsk8br\\/store_slideshow_large\\/Origin_100_Promo_Logo_Pj01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/jhifpuskfsm4gr\\/store_slideshow_large\\/Origin_100_All_100s_LineUP_Front_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/5yuztn7qxhn11r\\/store_slideshow_large\\/Origin_100_Promo_All100s_LineUp_Rear_PJ01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/an5wwm0nihyu0r\\/store_slideshow_large\\/Origin_100_Promo_Exploration_Promo_shot_PJ05-Min.jpg"]', 'pad_type': 'XS', 'game_version': '4.0', 'date_added': 1703505653, 'date_modified': 1741629801, 'company_name': 'Origin Jumpworks'}
2025-07-14 02:30:04,782 [INFO] Trying alternative cargo fields, found 135 vehicles
2025-07-14 02:30:04,782 [INFO] Found 135 vehicles with cargo capacity
2025-07-14 02:30:04,782 [INFO] Returning 25 autocomplete options
2025-07-14 02:30:05,762 [INFO] Vehicle autocomplete called with: '8'
2025-07-14 02:30:05,762 [INFO] Sample vehicle keys: ['id', 'id_company', 'id_parent', 'ids_vehicles_loaners', 'name', 'name_full', 'slug', 'uuid', 'scu', 'crew', 'mass', 'width', 'height', 'length', 'fuel_quantum', 'fuel_hydrogen', 'container_sizes', 'is_addon', 'is_boarding', 'is_bomber', 'is_cargo', 'is_carrier', 'is_civilian', 'is_concept', 'is_construction', 'is_datarunner', 'is_docking', 'is_emp', 'is_exploration', 'is_ground_vehicle', 'is_hangar', 'is_industrial', 'is_interdiction', 'is_loading_dock', 'is_medical', 'is_military', 'is_mining', 'is_passenger', 'is_qed', 'is_racing', 'is_refinery', 'is_refuel', 'is_repair', 'is_research', 'is_salvage', 'is_scanning', 'is_science', 'is_showdown_winner', 'is_spaceship', 'is_starter', 'is_stealth', 'is_tractor_beam', 'is_quantum_capable', 'url_photo', 'url_store', 'url_brochure', 'url_hotsite', 'url_video', 'url_photos', 'pad_type', 'game_version', 'date_added', 'date_modified', 'company_name']
2025-07-14 02:30:05,762 [INFO] Sample vehicle: {'id': 1, 'id_company': 195, 'id_parent': 1, 'ids_vehicles_loaners': '', 'name': '100i', 'name_full': 'Origin 100i', 'slug': '100i', 'uuid': '6135a874-4cb1-4f49-9f29-5781e5991f2b', 'scu': 2, 'crew': '1', 'mass': 0, 'width': 12, 'height': 5, 'length': 19, 'fuel_quantum': 0, 'fuel_hydrogen': 0, 'container_sizes': '1,2', 'is_addon': 0, 'is_boarding': 0, 'is_bomber': 0, 'is_cargo': 0, 'is_carrier': 0, 'is_civilian': 1, 'is_concept': 0, 'is_construction': 0, 'is_datarunner': 0, 'is_docking': 0, 'is_emp': 0, 'is_exploration': 0, 'is_ground_vehicle': 0, 'is_hangar': 0, 'is_industrial': 0, 'is_interdiction': 0, 'is_loading_dock': 0, 'is_medical': 0, 'is_military': 0, 'is_mining': 0, 'is_passenger': 0, 'is_qed': 0, 'is_racing': 0, 'is_refinery': 0, 'is_refuel': 0, 'is_repair': 0, 'is_research': 0, 'is_salvage': 0, 'is_scanning': 0, 'is_science': 0, 'is_showdown_winner': 0, 'is_spaceship': 1, 'is_starter': 1, 'is_stealth': 0, 'is_tractor_beam': 0, 'is_quantum_capable': 1, 'url_photo': 'https://assets.uexcorp.space/img/vehicles/1/images/fc9508537140e21f7959e16f152c915cc8d32dc2.jpg', 'url_store': 'https://robertsspaceindustries.com/pledge/ships/origin-100/100i', 'url_brochure': 'https://robertsspaceindustries.com/media/sb217acbbj976r/source/Origin-100-Series-Brochure-FINAL.pdf', 'url_hotsite': 'https://robertsspaceindustries.com/comm-link//16505-Introducing-The-Origin-100-Series', 'url_video': 'https://www.youtube.com/watch?v=QGMxGdzYVY8', 'url_photos': '["https:\\/\\/media.robertsspaceindustries.com\\/cjnxt0sdt9m1z\\/store_slideshow_large.png","https:\\/\\/media.robertsspaceindustries.com\\/ikj3i9042ppst\\/store_slideshow_large.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/dl3bq4ot40edwr\\/store_slideshow_large\\/SKUs_100i_Starter_PLEDGE.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/bapnpk9usqxxhr\\/store_slideshow_large\\/100i.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/7ufjldd2bh6tir\\/store_slideshow_large\\/ORIG_100_Sketch_PJ01_MA_Will_FINAL-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/5vr9snbkr4ktdr\\/store_slideshow_large\\/Origin_100_Promo_Closeup_Front_Cover_PJ01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/faqfpklm68itvr\\/store_slideshow_large\\/Origin_100_Promo_Closeup_Back_Cover_PJ01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/gpiycmt5scluhr\\/store_slideshow_large\\/ORIG_100i_Interior_design_Shot_AL03_CC-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/41fid4n8h94a3r\\/store_slideshow_large\\/Origin_100_Promo_Closeup_Man_Vent_PJ02_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/442bjw94xsk8br\\/store_slideshow_large\\/Origin_100_Promo_Logo_Pj01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/jhifpuskfsm4gr\\/store_slideshow_large\\/Origin_100_All_100s_LineUP_Front_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/5yuztn7qxhn11r\\/store_slideshow_large\\/Origin_100_Promo_All100s_LineUp_Rear_PJ01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/an5wwm0nihyu0r\\/store_slideshow_large\\/Origin_100_Promo_Exploration_Promo_shot_PJ05-Min.jpg"]', 'pad_type': 'XS', 'game_version': '4.0', 'date_added': 1703505653, 'date_modified': 1741629801, 'company_name': 'Origin Jumpworks'}
2025-07-14 02:30:05,763 [INFO] Trying alternative cargo fields, found 135 vehicles
2025-07-14 02:30:05,763 [INFO] Found 135 vehicles with cargo capacity
2025-07-14 02:30:05,763 [INFO] Returning 3 autocomplete options
2025-07-14 02:30:06,597 [INFO] Vehicle autocomplete called with: '89'
2025-07-14 02:30:06,598 [INFO] Sample vehicle keys: ['id', 'id_company', 'id_parent', 'ids_vehicles_loaners', 'name', 'name_full', 'slug', 'uuid', 'scu', 'crew', 'mass', 'width', 'height', 'length', 'fuel_quantum', 'fuel_hydrogen', 'container_sizes', 'is_addon', 'is_boarding', 'is_bomber', 'is_cargo', 'is_carrier', 'is_civilian', 'is_concept', 'is_construction', 'is_datarunner', 'is_docking', 'is_emp', 'is_exploration', 'is_ground_vehicle', 'is_hangar', 'is_industrial', 'is_interdiction', 'is_loading_dock', 'is_medical', 'is_military', 'is_mining', 'is_passenger', 'is_qed', 'is_racing', 'is_refinery', 'is_refuel', 'is_repair', 'is_research', 'is_salvage', 'is_scanning', 'is_science', 'is_showdown_winner', 'is_spaceship', 'is_starter', 'is_stealth', 'is_tractor_beam', 'is_quantum_capable', 'url_photo', 'url_store', 'url_brochure', 'url_hotsite', 'url_video', 'url_photos', 'pad_type', 'game_version', 'date_added', 'date_modified', 'company_name']
2025-07-14 02:30:06,598 [INFO] Sample vehicle: {'id': 1, 'id_company': 195, 'id_parent': 1, 'ids_vehicles_loaners': '', 'name': '100i', 'name_full': 'Origin 100i', 'slug': '100i', 'uuid': '6135a874-4cb1-4f49-9f29-5781e5991f2b', 'scu': 2, 'crew': '1', 'mass': 0, 'width': 12, 'height': 5, 'length': 19, 'fuel_quantum': 0, 'fuel_hydrogen': 0, 'container_sizes': '1,2', 'is_addon': 0, 'is_boarding': 0, 'is_bomber': 0, 'is_cargo': 0, 'is_carrier': 0, 'is_civilian': 1, 'is_concept': 0, 'is_construction': 0, 'is_datarunner': 0, 'is_docking': 0, 'is_emp': 0, 'is_exploration': 0, 'is_ground_vehicle': 0, 'is_hangar': 0, 'is_industrial': 0, 'is_interdiction': 0, 'is_loading_dock': 0, 'is_medical': 0, 'is_military': 0, 'is_mining': 0, 'is_passenger': 0, 'is_qed': 0, 'is_racing': 0, 'is_refinery': 0, 'is_refuel': 0, 'is_repair': 0, 'is_research': 0, 'is_salvage': 0, 'is_scanning': 0, 'is_science': 0, 'is_showdown_winner': 0, 'is_spaceship': 1, 'is_starter': 1, 'is_stealth': 0, 'is_tractor_beam': 0, 'is_quantum_capable': 1, 'url_photo': 'https://assets.uexcorp.space/img/vehicles/1/images/fc9508537140e21f7959e16f152c915cc8d32dc2.jpg', 'url_store': 'https://robertsspaceindustries.com/pledge/ships/origin-100/100i', 'url_brochure': 'https://robertsspaceindustries.com/media/sb217acbbj976r/source/Origin-100-Series-Brochure-FINAL.pdf', 'url_hotsite': 'https://robertsspaceindustries.com/comm-link//16505-Introducing-The-Origin-100-Series', 'url_video': 'https://www.youtube.com/watch?v=QGMxGdzYVY8', 'url_photos': '["https:\\/\\/media.robertsspaceindustries.com\\/cjnxt0sdt9m1z\\/store_slideshow_large.png","https:\\/\\/media.robertsspaceindustries.com\\/ikj3i9042ppst\\/store_slideshow_large.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/dl3bq4ot40edwr\\/store_slideshow_large\\/SKUs_100i_Starter_PLEDGE.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/bapnpk9usqxxhr\\/store_slideshow_large\\/100i.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/7ufjldd2bh6tir\\/store_slideshow_large\\/ORIG_100_Sketch_PJ01_MA_Will_FINAL-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/5vr9snbkr4ktdr\\/store_slideshow_large\\/Origin_100_Promo_Closeup_Front_Cover_PJ01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/faqfpklm68itvr\\/store_slideshow_large\\/Origin_100_Promo_Closeup_Back_Cover_PJ01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/gpiycmt5scluhr\\/store_slideshow_large\\/ORIG_100i_Interior_design_Shot_AL03_CC-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/41fid4n8h94a3r\\/store_slideshow_large\\/Origin_100_Promo_Closeup_Man_Vent_PJ02_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/442bjw94xsk8br\\/store_slideshow_large\\/Origin_100_Promo_Logo_Pj01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/jhifpuskfsm4gr\\/store_slideshow_large\\/Origin_100_All_100s_LineUP_Front_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/5yuztn7qxhn11r\\/store_slideshow_large\\/Origin_100_Promo_All100s_LineUp_Rear_PJ01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/an5wwm0nihyu0r\\/store_slideshow_large\\/Origin_100_Promo_Exploration_Promo_shot_PJ05-Min.jpg"]', 'pad_type': 'XS', 'game_version': '4.0', 'date_added': 1703505653, 'date_modified': 1741629801, 'company_name': 'Origin Jumpworks'}
2025-07-14 02:30:06,599 [INFO] Trying alternative cargo fields, found 135 vehicles
2025-07-14 02:30:06,599 [INFO] Found 135 vehicles with cargo capacity
2025-07-14 02:30:06,599 [INFO] Returning 1 autocomplete options
2025-07-14 02:30:51,361 [INFO] Loaded cog: applytoaegis.py
2025-07-14 02:30:51,362 [INFO] Loaded cog: embedgen.py
2025-07-14 02:30:51,363 [INFO] Loaded cog: fleetdata.py
2025-07-14 02:30:51,366 [INFO] Loaded cog: giveaway.py
2025-07-14 02:30:51,369 [INFO] Loaded cog: moderation.py
2025-07-14 02:30:51,370 [INFO] Loaded cog: rolerequest.py
2025-07-14 02:30:51,371 [INFO] Loaded cog: rosters.py
2025-07-14 02:30:51,373 [INFO] Loaded cog: shipgame.py
2025-07-14 02:30:51,374 [INFO] Loaded cog: storagetracker.py
2025-07-14 02:31:02,706 [INFO] UEX cache refreshed successfully
2025-07-14 02:31:02,706 [INFO] Loaded cog: uex_trade.py
2025-07-14 02:31:02,708 [INFO] Loaded cog: voice.py
2025-07-14 02:31:02,711 [INFO] Loaded cog: voicetracker.py
2025-07-14 02:31:02,712 [INFO] Loaded cog: welcome.py
2025-07-14 02:31:02,712 [INFO] logging in using static token
2025-07-14 02:31:03,464 [INFO] Shard ID None has connected to Gateway (Session ID: d3d6d2f0cc118109c022fae430f60efe).
2025-07-14 02:31:04,746 [INFO] Vehicle autocomplete called with: '8'
2025-07-14 02:31:04,746 [INFO] Sample vehicle keys: ['id', 'id_company', 'id_parent', 'ids_vehicles_loaners', 'name', 'name_full', 'slug', 'uuid', 'scu', 'crew', 'mass', 'width', 'height', 'length', 'fuel_quantum', 'fuel_hydrogen', 'container_sizes', 'is_addon', 'is_boarding', 'is_bomber', 'is_cargo', 'is_carrier', 'is_civilian', 'is_concept', 'is_construction', 'is_datarunner', 'is_docking', 'is_emp', 'is_exploration', 'is_ground_vehicle', 'is_hangar', 'is_industrial', 'is_interdiction', 'is_loading_dock', 'is_medical', 'is_military', 'is_mining', 'is_passenger', 'is_qed', 'is_racing', 'is_refinery', 'is_refuel', 'is_repair', 'is_research', 'is_salvage', 'is_scanning', 'is_science', 'is_showdown_winner', 'is_spaceship', 'is_starter', 'is_stealth', 'is_tractor_beam', 'is_quantum_capable', 'url_photo', 'url_store', 'url_brochure', 'url_hotsite', 'url_video', 'url_photos', 'pad_type', 'game_version', 'date_added', 'date_modified', 'company_name']
2025-07-14 02:31:04,746 [INFO] Sample vehicle: {'id': 1, 'id_company': 195, 'id_parent': 1, 'ids_vehicles_loaners': '', 'name': '100i', 'name_full': 'Origin 100i', 'slug': '100i', 'uuid': '6135a874-4cb1-4f49-9f29-5781e5991f2b', 'scu': 2, 'crew': '1', 'mass': 0, 'width': 12, 'height': 5, 'length': 19, 'fuel_quantum': 0, 'fuel_hydrogen': 0, 'container_sizes': '1,2', 'is_addon': 0, 'is_boarding': 0, 'is_bomber': 0, 'is_cargo': 0, 'is_carrier': 0, 'is_civilian': 1, 'is_concept': 0, 'is_construction': 0, 'is_datarunner': 0, 'is_docking': 0, 'is_emp': 0, 'is_exploration': 0, 'is_ground_vehicle': 0, 'is_hangar': 0, 'is_industrial': 0, 'is_interdiction': 0, 'is_loading_dock': 0, 'is_medical': 0, 'is_military': 0, 'is_mining': 0, 'is_passenger': 0, 'is_qed': 0, 'is_racing': 0, 'is_refinery': 0, 'is_refuel': 0, 'is_repair': 0, 'is_research': 0, 'is_salvage': 0, 'is_scanning': 0, 'is_science': 0, 'is_showdown_winner': 0, 'is_spaceship': 1, 'is_starter': 1, 'is_stealth': 0, 'is_tractor_beam': 0, 'is_quantum_capable': 1, 'url_photo': 'https://assets.uexcorp.space/img/vehicles/1/images/fc9508537140e21f7959e16f152c915cc8d32dc2.jpg', 'url_store': 'https://robertsspaceindustries.com/pledge/ships/origin-100/100i', 'url_brochure': 'https://robertsspaceindustries.com/media/sb217acbbj976r/source/Origin-100-Series-Brochure-FINAL.pdf', 'url_hotsite': 'https://robertsspaceindustries.com/comm-link//16505-Introducing-The-Origin-100-Series', 'url_video': 'https://www.youtube.com/watch?v=QGMxGdzYVY8', 'url_photos': '["https:\\/\\/media.robertsspaceindustries.com\\/cjnxt0sdt9m1z\\/store_slideshow_large.png","https:\\/\\/media.robertsspaceindustries.com\\/ikj3i9042ppst\\/store_slideshow_large.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/dl3bq4ot40edwr\\/store_slideshow_large\\/SKUs_100i_Starter_PLEDGE.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/bapnpk9usqxxhr\\/store_slideshow_large\\/100i.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/7ufjldd2bh6tir\\/store_slideshow_large\\/ORIG_100_Sketch_PJ01_MA_Will_FINAL-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/5vr9snbkr4ktdr\\/store_slideshow_large\\/Origin_100_Promo_Closeup_Front_Cover_PJ01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/faqfpklm68itvr\\/store_slideshow_large\\/Origin_100_Promo_Closeup_Back_Cover_PJ01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/gpiycmt5scluhr\\/store_slideshow_large\\/ORIG_100i_Interior_design_Shot_AL03_CC-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/41fid4n8h94a3r\\/store_slideshow_large\\/Origin_100_Promo_Closeup_Man_Vent_PJ02_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/442bjw94xsk8br\\/store_slideshow_large\\/Origin_100_Promo_Logo_Pj01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/jhifpuskfsm4gr\\/store_slideshow_large\\/Origin_100_All_100s_LineUP_Front_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/5yuztn7qxhn11r\\/store_slideshow_large\\/Origin_100_Promo_All100s_LineUp_Rear_PJ01_sm-Min.jpg","https:\\/\\/robertsspaceindustries.com\\/media\\/an5wwm0nihyu0r\\/store_slideshow_large\\/Origin_100_Promo_Exploration_Promo_shot_PJ05-Min.jpg"]', 'pad_type': 'XS', 'game_version': '4.0', 'date_added': 1703505653, 'date_modified': 1741629801, 'company_name': 'Origin Jumpworks'}
2025-07-14 02:31:04,747 [INFO] Trying alternative cargo fields, found 135 vehicles
2025-07-14 02:31:04,747 [INFO] Found 135 vehicles with cargo capacity
2025-07-14 02:31:04,747 [INFO] Returning 3 autocomplete options
2025-07-14 02:31:05,474 [INFO] Logged in as Aegis Nox Bot
2025-07-14 02:31:05,806 [INFO] Synced 29 slash commands globally.
2025-07-14 02:31:05,806 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 02:31:43,877 [INFO] Loaded cog: applytoaegis.py
2025-07-14 02:31:43,879 [INFO] Loaded cog: embedgen.py
2025-07-14 02:31:43,880 [INFO] Loaded cog: fleetdata.py
2025-07-14 02:31:43,881 [INFO] Loaded cog: giveaway.py
2025-07-14 02:31:43,885 [INFO] Loaded cog: moderation.py
2025-07-14 02:31:43,887 [INFO] Loaded cog: rolerequest.py
2025-07-14 02:31:43,889 [INFO] Loaded cog: rosters.py
2025-07-14 02:31:43,890 [INFO] Loaded cog: shipgame.py
2025-07-14 02:31:43,891 [INFO] Loaded cog: storagetracker.py
2025-07-14 02:31:54,937 [INFO] UEX cache refreshed successfully
2025-07-14 02:31:54,937 [INFO] Loaded cog: uex_trade.py
2025-07-14 02:31:54,939 [INFO] Loaded cog: voice.py
2025-07-14 02:31:54,942 [INFO] Loaded cog: voicetracker.py
2025-07-14 02:31:54,942 [INFO] Loaded cog: welcome.py
2025-07-14 02:31:54,942 [INFO] logging in using static token
2025-07-14 02:31:55,580 [INFO] Shard ID None has connected to Gateway (Session ID: 005531dc5401207f41fde7e93c7a2a83).
2025-07-14 02:31:57,585 [INFO] Logged in as Aegis Nox Bot
2025-07-14 02:31:57,854 [INFO] Synced 29 slash commands globally.
2025-07-14 02:31:57,855 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 02:32:02,739 [INFO] Found 74 tradeable commodities
2025-07-14 02:32:02,740 [INFO] Commodity prices cache has 169 entries
2025-07-14 02:32:02,741 [INFO] Generated 0 total routes
2025-07-14 02:32:45,868 [INFO] Loaded cog: applytoaegis.py
2025-07-14 02:32:45,870 [INFO] Loaded cog: embedgen.py
2025-07-14 02:32:45,871 [INFO] Loaded cog: fleetdata.py
2025-07-14 02:32:45,874 [INFO] Loaded cog: giveaway.py
2025-07-14 02:32:45,878 [INFO] Loaded cog: moderation.py
2025-07-14 02:32:45,880 [INFO] Loaded cog: rolerequest.py
2025-07-14 02:32:45,881 [INFO] Loaded cog: rosters.py
2025-07-14 02:32:45,883 [INFO] Loaded cog: shipgame.py
2025-07-14 02:32:45,884 [INFO] Loaded cog: storagetracker.py
2025-07-14 02:32:57,041 [INFO] UEX cache refreshed successfully
2025-07-14 02:32:57,041 [INFO] Loaded cog: uex_trade.py
2025-07-14 02:32:57,044 [INFO] Loaded cog: voice.py
2025-07-14 02:32:57,047 [INFO] Loaded cog: voicetracker.py
2025-07-14 02:32:57,048 [INFO] Loaded cog: welcome.py
2025-07-14 02:32:57,048 [INFO] logging in using static token
2025-07-14 02:32:57,895 [INFO] Shard ID None has connected to Gateway (Session ID: 1a9707c88b66d608ec5a84dc435ff26f).
2025-07-14 02:32:59,917 [INFO] Logged in as Aegis Nox Bot
2025-07-14 02:33:00,111 [INFO] Synced 29 slash commands globally.
2025-07-14 02:33:00,112 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 02:34:32,243 [INFO] Found 74 tradeable commodities
2025-07-14 02:34:32,243 [INFO] Commodity prices cache has 169 entries
2025-07-14 02:34:32,243 [INFO] Commodity Agricium: 25 price entries
2025-07-14 02:34:32,244 [INFO] Commodity Agricium: 0 buy locations, 0 sell locations
2025-07-14 02:34:32,244 [INFO] Commodity Agricultural Supplies: 26 price entries
2025-07-14 02:34:32,244 [INFO] Commodity Agricultural Supplies: 0 buy locations, 0 sell locations
2025-07-14 02:34:32,244 [INFO] Commodity Altruciatoxin: 20 price entries
2025-07-14 02:34:32,244 [INFO] Commodity Altruciatoxin: 0 buy locations, 0 sell locations
2025-07-14 02:34:32,244 [INFO] Commodity Aluminum: 37 price entries
2025-07-14 02:34:32,244 [INFO] Commodity Aluminum: 0 buy locations, 0 sell locations
2025-07-14 02:34:32,245 [INFO] Commodity Amioshi Plague: 10 price entries
2025-07-14 02:34:32,245 [INFO] Commodity Amioshi Plague: 0 buy locations, 0 sell locations
2025-07-14 02:34:32,245 [INFO] Generated 0 total routes
2025-07-14 02:35:29,230 [INFO] Loaded cog: applytoaegis.py
2025-07-14 02:35:29,232 [INFO] Loaded cog: embedgen.py
2025-07-14 02:35:29,233 [INFO] Loaded cog: fleetdata.py
2025-07-14 02:35:29,235 [INFO] Loaded cog: giveaway.py
2025-07-14 02:35:29,246 [INFO] Loaded cog: moderation.py
2025-07-14 02:35:29,247 [INFO] Loaded cog: rolerequest.py
2025-07-14 02:35:29,249 [INFO] Loaded cog: rosters.py
2025-07-14 02:35:29,250 [INFO] Loaded cog: shipgame.py
2025-07-14 02:35:29,252 [INFO] Loaded cog: storagetracker.py
2025-07-14 02:35:40,277 [INFO] UEX cache refreshed successfully
2025-07-14 02:35:40,278 [INFO] Loaded cog: uex_trade.py
2025-07-14 02:35:40,279 [INFO] Loaded cog: voice.py
2025-07-14 02:35:40,283 [INFO] Loaded cog: voicetracker.py
2025-07-14 02:35:40,284 [INFO] Loaded cog: welcome.py
2025-07-14 02:35:40,284 [INFO] logging in using static token
2025-07-14 02:35:41,009 [INFO] Shard ID None has connected to Gateway (Session ID: 22dbc3fc62cc7eacd01bd33b07c25826).
2025-07-14 02:35:43,019 [INFO] Logged in as Aegis Nox Bot
2025-07-14 02:35:43,271 [INFO] Synced 29 slash commands globally.
2025-07-14 02:35:43,271 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 02:38:39,787 [INFO] Found 74 tradeable commodities
2025-07-14 02:38:39,787 [INFO] Commodity prices cache has 169 entries
2025-07-14 02:38:39,787 [INFO] Commodity Agricium: 25 price entries
2025-07-14 02:38:39,787 [INFO] Sample price data keys: ['id', 'id_commodity', 'id_star_system', 'id_planet', 'id_orbit', 'id_moon', 'id_city', 'id_outpost', 'id_poi', 'id_terminal', 'id_faction', 'price_buy', 'price_buy_min', 'price_buy_min_week', 'price_buy_min_month', 'price_buy_max', 'price_buy_max_week', 'price_buy_max_month', 'price_buy_avg', 'price_buy_avg_week', 'price_buy_avg_month', 'price_buy_users', 'price_buy_users_rows', 'price_sell', 'price_sell_min', 'price_sell_min_week', 'price_sell_min_month', 'price_sell_max', 'price_sell_max_week', 'price_sell_max_month', 'price_sell_avg', 'price_sell_avg_week', 'price_sell_avg_month', 'price_sell_users', 'price_sell_users_rows', 'scu_buy', 'scu_buy_min', 'scu_buy_min_week', 'scu_buy_min_month', 'scu_buy_max', 'scu_buy_max_week', 'scu_buy_max_month', 'scu_buy_avg', 'scu_buy_avg_week', 'scu_buy_avg_month', 'scu_buy_users', 'scu_buy_users_rows', 'scu_sell_stock', 'scu_sell_stock_avg', 'scu_sell_stock_avg_week', 'scu_sell_stock_avg_month', 'scu_sell', 'scu_sell_min', 'scu_sell_min_week', 'scu_sell_min_month', 'scu_sell_max', 'scu_sell_max_week', 'scu_sell_max_month', 'scu_sell_avg', 'scu_sell_avg_week', 'scu_sell_avg_month', 'scu_sell_users', 'scu_sell_users_rows', 'status_buy', 'status_buy_min', 'status_buy_min_week', 'status_buy_min_month', 'status_buy_max', 'status_buy_max_week', 'status_buy_max_month', 'status_buy_avg', 'status_buy_avg_week', 'status_buy_avg_month', 'status_sell', 'status_sell_min', 'status_sell_min_week', 'status_sell_min_month', 'status_sell_max', 'status_sell_max_week', 'status_sell_max_month', 'status_sell_avg', 'status_sell_avg_week', 'status_sell_avg_month', 'volatility_buy', 'volatility_sell', 'volatility_price_buy', 'volatility_price_sell', 'volatility_scu_buy', 'volatility_scu_sell', 'faction_affinity', 'container_sizes', 'game_version', 'date_added', 'date_modified', 'commodity_name', 'commodity_code', 'commodity_slug', 'star_system_name', 'planet_name', 'orbit_name', 'moon_name', 'space_station_name', 'city_name', 'outpost_name', 'poi_name', 'faction_name', 'terminal_name', 'terminal_slug', 'terminal_code', 'terminal_mcs', 'terminal_is_player_owned']
2025-07-14 02:38:39,788 [INFO] Sample price data: {'id': 4, 'id_commodity': 1, 'id_star_system': 68, 'id_planet': 4, 'id_orbit': 4, 'id_moon': 0, 'id_city': 1, 'id_outpost': 0, 'id_poi': 0, 'id_terminal': 12, 'id_faction': 23, 'price_buy': 0, 'price_buy_min': 0, 'price_buy_min_week': 0, 'price_buy_min_month': 0, 'price_buy_max': 0, 'price_buy_max_week': 0, 'price_buy_max_month': 0, 'price_buy_avg': 0, 'price_buy_avg_week': 0, 'price_buy_avg_month': 0, 'price_buy_users': 0, 'price_buy_users_rows': 0, 'price_sell': 2327, 'price_sell_min': 2327, 'price_sell_min_week': 2327, 'price_sell_min_month': 2327, 'price_sell_max': 2327, 'price_sell_max_week': 2327, 'price_sell_max_month': 2327, 'price_sell_avg': 2327, 'price_sell_avg_week': 2327, 'price_sell_avg_month': 2327, 'price_sell_users': 0, 'price_sell_users_rows': 0, 'scu_buy': 0, 'scu_buy_min': 0, 'scu_buy_min_week': 0, 'scu_buy_min_month': 0, 'scu_buy_max': 0, 'scu_buy_max_week': 0, 'scu_buy_max_month': 0, 'scu_buy_avg': 0, 'scu_buy_avg_week': 0, 'scu_buy_avg_month': 0, 'scu_buy_users': 0, 'scu_buy_users_rows': 0, 'scu_sell_stock': 19, 'scu_sell_stock_avg': 0, 'scu_sell_stock_avg_week': 0, 'scu_sell_stock_avg_month': 0, 'scu_sell': 0, 'scu_sell_min': 0, 'scu_sell_min_week': 0, 'scu_sell_min_month': 0, 'scu_sell_max': 0, 'scu_sell_max_week': 0, 'scu_sell_max_month': 0, 'scu_sell_avg': 0, 'scu_sell_avg_week': 0, 'scu_sell_avg_month': 0, 'scu_sell_users': 0, 'scu_sell_users_rows': 0, 'status_buy': 0, 'status_buy_min': 0, 'status_buy_min_week': 0, 'status_buy_min_month': 0, 'status_buy_max': 0, 'status_buy_max_week': 0, 'status_buy_max_month': 0, 'status_buy_avg': 0, 'status_buy_avg_week': 0, 'status_buy_avg_month': 0, 'status_sell': 1, 'status_sell_min': 1, 'status_sell_min_week': 1, 'status_sell_min_month': 1, 'status_sell_max': 1, 'status_sell_max_week': 1, 'status_sell_max_month': 1, 'status_sell_avg': 1, 'status_sell_avg_week': 1, 'status_sell_avg_month': 1, 'volatility_buy': 0, 'volatility_sell': 0, 'volatility_price_buy': 0, 'volatility_price_sell': 0, 'volatility_scu_buy': 0, 'volatility_scu_sell': 0, 'faction_affinity': 0, 'container_sizes': '1,2,4,8,16,24,32', 'game_version': '4.2', 'date_added': 1703514825, 'date_modified': 1751925521, 'commodity_name': 'Agricium', 'commodity_code': 'AGRI', 'commodity_slug': 'agricium', 'star_system_name': 'Stanton', 'planet_name': 'ArcCorp', 'orbit_name': 'ArcCorp', 'moon_name': None, 'space_station_name': None, 'city_name': 'Area 18', 'outpost_name': None, 'poi_name': None, 'faction_name': 'United Empire of Earth', 'terminal_name': 'TDD - Trade and Development Division - Area 18', 'terminal_slug': 'tdd-trade-and-development-division-area-18', 'terminal_code': 'TDA18', 'terminal_mcs': 0, 'terminal_is_player_owned': 0}
2025-07-14 02:38:39,788 [INFO] Commodity Agricium: 0 buy locations, 0 sell locations
2025-07-14 02:38:39,788 [INFO] Commodity Agricultural Supplies: 26 price entries
2025-07-14 02:38:39,788 [INFO] Sample price data keys: ['id', 'id_commodity', 'id_star_system', 'id_planet', 'id_orbit', 'id_moon', 'id_city', 'id_outpost', 'id_poi', 'id_terminal', 'id_faction', 'price_buy', 'price_buy_min', 'price_buy_min_week', 'price_buy_min_month', 'price_buy_max', 'price_buy_max_week', 'price_buy_max_month', 'price_buy_avg', 'price_buy_avg_week', 'price_buy_avg_month', 'price_buy_users', 'price_buy_users_rows', 'price_sell', 'price_sell_min', 'price_sell_min_week', 'price_sell_min_month', 'price_sell_max', 'price_sell_max_week', 'price_sell_max_month', 'price_sell_avg', 'price_sell_avg_week', 'price_sell_avg_month', 'price_sell_users', 'price_sell_users_rows', 'scu_buy', 'scu_buy_min', 'scu_buy_min_week', 'scu_buy_min_month', 'scu_buy_max', 'scu_buy_max_week', 'scu_buy_max_month', 'scu_buy_avg', 'scu_buy_avg_week', 'scu_buy_avg_month', 'scu_buy_users', 'scu_buy_users_rows', 'scu_sell_stock', 'scu_sell_stock_avg', 'scu_sell_stock_avg_week', 'scu_sell_stock_avg_month', 'scu_sell', 'scu_sell_min', 'scu_sell_min_week', 'scu_sell_min_month', 'scu_sell_max', 'scu_sell_max_week', 'scu_sell_max_month', 'scu_sell_avg', 'scu_sell_avg_week', 'scu_sell_avg_month', 'scu_sell_users', 'scu_sell_users_rows', 'status_buy', 'status_buy_min', 'status_buy_min_week', 'status_buy_min_month', 'status_buy_max', 'status_buy_max_week', 'status_buy_max_month', 'status_buy_avg', 'status_buy_avg_week', 'status_buy_avg_month', 'status_sell', 'status_sell_min', 'status_sell_min_week', 'status_sell_min_month', 'status_sell_max', 'status_sell_max_week', 'status_sell_max_month', 'status_sell_avg', 'status_sell_avg_week', 'status_sell_avg_month', 'volatility_buy', 'volatility_sell', 'volatility_price_buy', 'volatility_price_sell', 'volatility_scu_buy', 'volatility_scu_sell', 'faction_affinity', 'container_sizes', 'game_version', 'date_added', 'date_modified', 'commodity_name', 'commodity_code', 'commodity_slug', 'star_system_name', 'planet_name', 'orbit_name', 'moon_name', 'space_station_name', 'city_name', 'outpost_name', 'poi_name', 'faction_name', 'terminal_name', 'terminal_slug', 'terminal_code', 'terminal_mcs', 'terminal_is_player_owned']
2025-07-14 02:38:39,789 [INFO] Sample price data: {'id': 32, 'id_commodity': 3, 'id_star_system': 68, 'id_planet': 190, 'id_orbit': 190, 'id_moon': 0, 'id_city': 4, 'id_outpost': 0, 'id_poi': 0, 'id_terminal': 89, 'id_faction': 23, 'price_buy': 0, 'price_buy_min': 0, 'price_buy_min_week': 0, 'price_buy_min_month': 0, 'price_buy_max': 0, 'price_buy_max_week': 0, 'price_buy_max_month': 0, 'price_buy_avg': 0, 'price_buy_avg_week': 0, 'price_buy_avg_month': 0, 'price_buy_users': 0, 'price_buy_users_rows': 0, 'price_sell': 302, 'price_sell_min': 302, 'price_sell_min_week': 302, 'price_sell_min_month': 302, 'price_sell_max': 302, 'price_sell_max_week': 302, 'price_sell_max_month': 302, 'price_sell_avg': 302, 'price_sell_avg_week': 302, 'price_sell_avg_month': 302, 'price_sell_users': 0, 'price_sell_users_rows': 0, 'scu_buy': 0, 'scu_buy_min': 0, 'scu_buy_min_week': 0, 'scu_buy_min_month': 0, 'scu_buy_max': 0, 'scu_buy_max_week': 0, 'scu_buy_max_month': 0, 'scu_buy_avg': 0, 'scu_buy_avg_week': 0, 'scu_buy_avg_month': 0, 'scu_buy_users': 0, 'scu_buy_users_rows': 0, 'scu_sell_stock': 0, 'scu_sell_stock_avg': 0, 'scu_sell_stock_avg_week': 0, 'scu_sell_stock_avg_month': 0, 'scu_sell': 0, 'scu_sell_min': 0, 'scu_sell_min_week': 0, 'scu_sell_min_month': 0, 'scu_sell_max': 0, 'scu_sell_max_week': 0, 'scu_sell_max_month': 0, 'scu_sell_avg': 0, 'scu_sell_avg_week': 0, 'scu_sell_avg_month': 0, 'scu_sell_users': 0, 'scu_sell_users_rows': 0, 'status_buy': 0, 'status_buy_min': 0, 'status_buy_min_week': 0, 'status_buy_min_month': 0, 'status_buy_max': 0, 'status_buy_max_week': 0, 'status_buy_max_month': 0, 'status_buy_avg': 0, 'status_buy_avg_week': 0, 'status_buy_avg_month': 0, 'status_sell': 1, 'status_sell_min': 1, 'status_sell_min_week': 1, 'status_sell_min_month': 1, 'status_sell_max': 1, 'status_sell_max_week': 1, 'status_sell_max_month': 1, 'status_sell_avg': 1, 'status_sell_avg_week': 1, 'status_sell_avg_month': 1, 'volatility_buy': 0, 'volatility_sell': 0, 'volatility_price_buy': 0, 'volatility_price_sell': 0, 'volatility_scu_buy': 0, 'volatility_scu_sell': 0, 'faction_affinity': 0, 'container_sizes': '1,2,4,8,16,24,32', 'game_version': '4.2', 'date_added': 1703515962, 'date_modified': 1752176628, 'commodity_name': 'Agricultural Supplies', 'commodity_code': 'AGRS', 'commodity_slug': 'agricultural-supplies', 'star_system_name': 'Stanton', 'planet_name': 'MicroTech', 'orbit_name': 'MicroTech', 'moon_name': None, 'space_station_name': None, 'city_name': 'New Babbage', 'outpost_name': None, 'poi_name': None, 'faction_name': 'United Empire of Earth', 'terminal_name': 'TDD - Trade and Development Division - Commons - New Babbage', 'terminal_slug': 'tdd-trade-and-development-division-commons-new-babbage', 'terminal_code': 'TDNEW', 'terminal_mcs': 0, 'terminal_is_player_owned': 0}
2025-07-14 02:38:39,789 [INFO] Commodity Agricultural Supplies: 0 buy locations, 0 sell locations
2025-07-14 02:38:39,790 [INFO] Commodity Altruciatoxin: 20 price entries
2025-07-14 02:38:39,790 [INFO] Sample price data keys: ['id', 'id_commodity', 'id_star_system', 'id_planet', 'id_orbit', 'id_moon', 'id_city', 'id_outpost', 'id_poi', 'id_terminal', 'id_faction', 'price_buy', 'price_buy_min', 'price_buy_min_week', 'price_buy_min_month', 'price_buy_max', 'price_buy_max_week', 'price_buy_max_month', 'price_buy_avg', 'price_buy_avg_week', 'price_buy_avg_month', 'price_buy_users', 'price_buy_users_rows', 'price_sell', 'price_sell_min', 'price_sell_min_week', 'price_sell_min_month', 'price_sell_max', 'price_sell_max_week', 'price_sell_max_month', 'price_sell_avg', 'price_sell_avg_week', 'price_sell_avg_month', 'price_sell_users', 'price_sell_users_rows', 'scu_buy', 'scu_buy_min', 'scu_buy_min_week', 'scu_buy_min_month', 'scu_buy_max', 'scu_buy_max_week', 'scu_buy_max_month', 'scu_buy_avg', 'scu_buy_avg_week', 'scu_buy_avg_month', 'scu_buy_users', 'scu_buy_users_rows', 'scu_sell_stock', 'scu_sell_stock_avg', 'scu_sell_stock_avg_week', 'scu_sell_stock_avg_month', 'scu_sell', 'scu_sell_min', 'scu_sell_min_week', 'scu_sell_min_month', 'scu_sell_max', 'scu_sell_max_week', 'scu_sell_max_month', 'scu_sell_avg', 'scu_sell_avg_week', 'scu_sell_avg_month', 'scu_sell_users', 'scu_sell_users_rows', 'status_buy', 'status_buy_min', 'status_buy_min_week', 'status_buy_min_month', 'status_buy_max', 'status_buy_max_week', 'status_buy_max_month', 'status_buy_avg', 'status_buy_avg_week', 'status_buy_avg_month', 'status_sell', 'status_sell_min', 'status_sell_min_week', 'status_sell_min_month', 'status_sell_max', 'status_sell_max_week', 'status_sell_max_month', 'status_sell_avg', 'status_sell_avg_week', 'status_sell_avg_month', 'volatility_buy', 'volatility_sell', 'volatility_price_buy', 'volatility_price_sell', 'volatility_scu_buy', 'volatility_scu_sell', 'faction_affinity', 'container_sizes', 'game_version', 'date_added', 'date_modified', 'commodity_name', 'commodity_code', 'commodity_slug', 'star_system_name', 'planet_name', 'orbit_name', 'moon_name', 'space_station_name', 'city_name', 'outpost_name', 'poi_name', 'faction_name', 'terminal_name', 'terminal_slug', 'terminal_code', 'terminal_mcs', 'terminal_is_player_owned']
2025-07-14 02:38:39,790 [INFO] Sample price data: {'id': 166, 'id_commodity': 4, 'id_star_system': 68, 'id_planet': 4, 'id_orbit': 4, 'id_moon': 74, 'id_city': 0, 'id_outpost': 45, 'id_poi': 0, 'id_terminal': 75, 'id_faction': 23, 'price_buy': 0, 'price_buy_min': 0, 'price_buy_min_week': 0, 'price_buy_min_month': 0, 'price_buy_max': 0, 'price_buy_max_week': 0, 'price_buy_max_month': 0, 'price_buy_avg': 0, 'price_buy_avg_week': 0, 'price_buy_avg_month': 0, 'price_buy_users': 0, 'price_buy_users_rows': 0, 'price_sell': 4446, 'price_sell_min': 4446, 'price_sell_min_week': 4446, 'price_sell_min_month': 4446, 'price_sell_max': 4446, 'price_sell_max_week': 4446, 'price_sell_max_month': 4446, 'price_sell_avg': 4446, 'price_sell_avg_week': 4446, 'price_sell_avg_month': 4446, 'price_sell_users': 0, 'price_sell_users_rows': 0, 'scu_buy': 0, 'scu_buy_min': 0, 'scu_buy_min_week': 0, 'scu_buy_min_month': 0, 'scu_buy_max': 0, 'scu_buy_max_week': 0, 'scu_buy_max_month': 0, 'scu_buy_avg': 0, 'scu_buy_avg_week': 0, 'scu_buy_avg_month': 0, 'scu_buy_users': 0, 'scu_buy_users_rows': 0, 'scu_sell_stock': 240, 'scu_sell_stock_avg': 240, 'scu_sell_stock_avg_week': 0, 'scu_sell_stock_avg_month': 240, 'scu_sell': 0, 'scu_sell_min': 280, 'scu_sell_min_week': 0, 'scu_sell_min_month': 280, 'scu_sell_max': 280, 'scu_sell_max_week': 0, 'scu_sell_max_month': 280, 'scu_sell_avg': 280, 'scu_sell_avg_week': 0, 'scu_sell_avg_month': 280, 'scu_sell_users': 0, 'scu_sell_users_rows': 0, 'status_buy': 0, 'status_buy_min': 0, 'status_buy_min_week': 0, 'status_buy_min_month': 0, 'status_buy_max': 0, 'status_buy_max_week': 0, 'status_buy_max_month': 0, 'status_buy_avg': 0, 'status_buy_avg_week': 0, 'status_buy_avg_month': 0, 'status_sell': 1, 'status_sell_min': 1, 'status_sell_min_week': 1, 'status_sell_min_month': 1, 'status_sell_max': 6, 'status_sell_max_week': 1, 'status_sell_max_month': 6, 'status_sell_avg': 4, 'status_sell_avg_week': 1, 'status_sell_avg_month': 4, 'volatility_buy': 0, 'volatility_sell': 0, 'volatility_price_buy': 0, 'volatility_price_sell': 0, 'volatility_scu_buy': 0, 'volatility_scu_sell': 0, 'faction_affinity': 0, 'container_sizes': '1,2,4,8,16,24', 'game_version': '4.2', 'date_added': 1703551048, 'date_modified': 1752215006, 'commodity_name': 'Altruciatoxin', 'commodity_code': 'AUTR', 'commodity_slug': 'altruciatoxin', 'star_system_name': 'Stanton', 'planet_name': 'ArcCorp', 'orbit_name': 'ArcCorp', 'moon_name': 'Wala', 'space_station_name': None, 'city_name': None, 'outpost_name': "Samson & Son's Salvage Center", 'poi_name': None, 'faction_name': 'United Empire of Earth', 'terminal_name': "Samson Son's Salvage Center", 'terminal_slug': 'samson--sons-salvage-center', 'terminal_code': 'SAMSO', 'terminal_mcs': 0, 'terminal_is_player_owned': 0}
2025-07-14 02:38:39,791 [INFO] Commodity Altruciatoxin: 0 buy locations, 0 sell locations
2025-07-14 02:38:39,791 [INFO] Commodity Aluminum: 37 price entries
2025-07-14 02:38:39,791 [INFO] Sample price data keys: ['id', 'id_commodity', 'id_star_system', 'id_planet', 'id_orbit', 'id_moon', 'id_city', 'id_outpost', 'id_poi', 'id_terminal', 'id_faction', 'price_buy', 'price_buy_min', 'price_buy_min_week', 'price_buy_min_month', 'price_buy_max', 'price_buy_max_week', 'price_buy_max_month', 'price_buy_avg', 'price_buy_avg_week', 'price_buy_avg_month', 'price_buy_users', 'price_buy_users_rows', 'price_sell', 'price_sell_min', 'price_sell_min_week', 'price_sell_min_month', 'price_sell_max', 'price_sell_max_week', 'price_sell_max_month', 'price_sell_avg', 'price_sell_avg_week', 'price_sell_avg_month', 'price_sell_users', 'price_sell_users_rows', 'scu_buy', 'scu_buy_min', 'scu_buy_min_week', 'scu_buy_min_month', 'scu_buy_max', 'scu_buy_max_week', 'scu_buy_max_month', 'scu_buy_avg', 'scu_buy_avg_week', 'scu_buy_avg_month', 'scu_buy_users', 'scu_buy_users_rows', 'scu_sell_stock', 'scu_sell_stock_avg', 'scu_sell_stock_avg_week', 'scu_sell_stock_avg_month', 'scu_sell', 'scu_sell_min', 'scu_sell_min_week', 'scu_sell_min_month', 'scu_sell_max', 'scu_sell_max_week', 'scu_sell_max_month', 'scu_sell_avg', 'scu_sell_avg_week', 'scu_sell_avg_month', 'scu_sell_users', 'scu_sell_users_rows', 'status_buy', 'status_buy_min', 'status_buy_min_week', 'status_buy_min_month', 'status_buy_max', 'status_buy_max_week', 'status_buy_max_month', 'status_buy_avg', 'status_buy_avg_week', 'status_buy_avg_month', 'status_sell', 'status_sell_min', 'status_sell_min_week', 'status_sell_min_month', 'status_sell_max', 'status_sell_max_week', 'status_sell_max_month', 'status_sell_avg', 'status_sell_avg_week', 'status_sell_avg_month', 'volatility_buy', 'volatility_sell', 'volatility_price_buy', 'volatility_price_sell', 'volatility_scu_buy', 'volatility_scu_sell', 'faction_affinity', 'container_sizes', 'game_version', 'date_added', 'date_modified', 'commodity_name', 'commodity_code', 'commodity_slug', 'star_system_name', 'planet_name', 'orbit_name', 'moon_name', 'space_station_name', 'city_name', 'outpost_name', 'poi_name', 'faction_name', 'terminal_name', 'terminal_slug', 'terminal_code', 'terminal_mcs', 'terminal_is_player_owned']
2025-07-14 02:38:39,791 [INFO] Sample price data: {'id': 2, 'id_commodity': 5, 'id_star_system': 68, 'id_planet': 4, 'id_orbit': 4, 'id_moon': 0, 'id_city': 1, 'id_outpost': 0, 'id_poi': 0, 'id_terminal': 12, 'id_faction': 23, 'price_buy': 0, 'price_buy_min': 0, 'price_buy_min_week': 0, 'price_buy_min_month': 0, 'price_buy_max': 0, 'price_buy_max_week': 0, 'price_buy_max_month': 0, 'price_buy_avg': 0, 'price_buy_avg_week': 0, 'price_buy_avg_month': 0, 'price_buy_users': 0, 'price_buy_users_rows': 0, 'price_sell': 291, 'price_sell_min': 291, 'price_sell_min_week': 291, 'price_sell_min_month': 291, 'price_sell_max': 291, 'price_sell_max_week': 291, 'price_sell_max_month': 291, 'price_sell_avg': 291, 'price_sell_avg_week': 291, 'price_sell_avg_month': 291, 'price_sell_users': 0, 'price_sell_users_rows': 0, 'scu_buy': 0, 'scu_buy_min': 0, 'scu_buy_min_week': 0, 'scu_buy_min_month': 0, 'scu_buy_max': 0, 'scu_buy_max_week': 0, 'scu_buy_max_month': 0, 'scu_buy_avg': 0, 'scu_buy_avg_week': 0, 'scu_buy_avg_month': 0, 'scu_buy_users': 0, 'scu_buy_users_rows': 0, 'scu_sell_stock': 5, 'scu_sell_stock_avg': 10, 'scu_sell_stock_avg_week': 5, 'scu_sell_stock_avg_month': 10, 'scu_sell': 0, 'scu_sell_min': 35, 'scu_sell_min_week': 35, 'scu_sell_min_month': 35, 'scu_sell_max': 98, 'scu_sell_max_week': 35, 'scu_sell_max_month': 98, 'scu_sell_avg': 67, 'scu_sell_avg_week': 35, 'scu_sell_avg_month': 67, 'scu_sell_users': 0, 'scu_sell_users_rows': 0, 'status_buy': 0, 'status_buy_min': 0, 'status_buy_min_week': 0, 'status_buy_min_month': 0, 'status_buy_max': 0, 'status_buy_max_week': 0, 'status_buy_max_month': 0, 'status_buy_avg': 0, 'status_buy_avg_week': 0, 'status_buy_avg_month': 0, 'status_sell': 1, 'status_sell_min': 1, 'status_sell_min_week': 1, 'status_sell_min_month': 1, 'status_sell_max': 1, 'status_sell_max_week': 1, 'status_sell_max_month': 1, 'status_sell_avg': 1, 'status_sell_avg_week': 1, 'status_sell_avg_month': 1, 'volatility_buy': 0, 'volatility_sell': 0, 'volatility_price_buy': 0, 'volatility_price_sell': 0, 'volatility_scu_buy': 0, 'volatility_scu_sell': 31.5, 'faction_affinity': 0, 'container_sizes': '1,2,4,8,16,24,32', 'game_version': '4.2', 'date_added': 1703514825, 'date_modified': 1751925521, 'commodity_name': 'Aluminum', 'commodity_code': 'ALUM', 'commodity_slug': 'aluminum', 'star_system_name': 'Stanton', 'planet_name': 'ArcCorp', 'orbit_name': 'ArcCorp', 'moon_name': None, 'space_station_name': None, 'city_name': 'Area 18', 'outpost_name': None, 'poi_name': None, 'faction_name': 'United Empire of Earth', 'terminal_name': 'TDD - Trade and Development Division - Area 18', 'terminal_slug': 'tdd-trade-and-development-division-area-18', 'terminal_code': 'TDA18', 'terminal_mcs': 0, 'terminal_is_player_owned': 0}
2025-07-14 02:38:39,791 [INFO] Commodity Aluminum: 0 buy locations, 0 sell locations
2025-07-14 02:38:39,792 [INFO] Commodity Amioshi Plague: 10 price entries
2025-07-14 02:38:39,792 [INFO] Sample price data keys: ['id', 'id_commodity', 'id_star_system', 'id_planet', 'id_orbit', 'id_moon', 'id_city', 'id_outpost', 'id_poi', 'id_terminal', 'id_faction', 'price_buy', 'price_buy_min', 'price_buy_min_week', 'price_buy_min_month', 'price_buy_max', 'price_buy_max_week', 'price_buy_max_month', 'price_buy_avg', 'price_buy_avg_week', 'price_buy_avg_month', 'price_buy_users', 'price_buy_users_rows', 'price_sell', 'price_sell_min', 'price_sell_min_week', 'price_sell_min_month', 'price_sell_max', 'price_sell_max_week', 'price_sell_max_month', 'price_sell_avg', 'price_sell_avg_week', 'price_sell_avg_month', 'price_sell_users', 'price_sell_users_rows', 'scu_buy', 'scu_buy_min', 'scu_buy_min_week', 'scu_buy_min_month', 'scu_buy_max', 'scu_buy_max_week', 'scu_buy_max_month', 'scu_buy_avg', 'scu_buy_avg_week', 'scu_buy_avg_month', 'scu_buy_users', 'scu_buy_users_rows', 'scu_sell_stock', 'scu_sell_stock_avg', 'scu_sell_stock_avg_week', 'scu_sell_stock_avg_month', 'scu_sell', 'scu_sell_min', 'scu_sell_min_week', 'scu_sell_min_month', 'scu_sell_max', 'scu_sell_max_week', 'scu_sell_max_month', 'scu_sell_avg', 'scu_sell_avg_week', 'scu_sell_avg_month', 'scu_sell_users', 'scu_sell_users_rows', 'status_buy', 'status_buy_min', 'status_buy_min_week', 'status_buy_min_month', 'status_buy_max', 'status_buy_max_week', 'status_buy_max_month', 'status_buy_avg', 'status_buy_avg_week', 'status_buy_avg_month', 'status_sell', 'status_sell_min', 'status_sell_min_week', 'status_sell_min_month', 'status_sell_max', 'status_sell_max_week', 'status_sell_max_month', 'status_sell_avg', 'status_sell_avg_week', 'status_sell_avg_month', 'volatility_buy', 'volatility_sell', 'volatility_price_buy', 'volatility_price_sell', 'volatility_scu_buy', 'volatility_scu_sell', 'faction_affinity', 'container_sizes', 'game_version', 'date_added', 'date_modified', 'commodity_name', 'commodity_code', 'commodity_slug', 'star_system_name', 'planet_name', 'orbit_name', 'moon_name', 'space_station_name', 'city_name', 'outpost_name', 'poi_name', 'faction_name', 'terminal_name', 'terminal_slug', 'terminal_code', 'terminal_mcs', 'terminal_is_player_owned']
2025-07-14 02:38:39,792 [INFO] Sample price data: {'id': 800, 'id_commodity': 7, 'id_star_system': 68, 'id_planet': 190, 'id_orbit': 190, 'id_moon': 23, 'id_city': 0, 'id_outpost': 43, 'id_poi': 0, 'id_terminal': 73, 'id_faction': 23, 'price_buy': 0, 'price_buy_min': 0, 'price_buy_min_week': 0, 'price_buy_min_month': 0, 'price_buy_max': 0, 'price_buy_max_week': 0, 'price_buy_max_month': 0, 'price_buy_avg': 0, 'price_buy_avg_week': 0, 'price_buy_avg_month': 0, 'price_buy_users': 0, 'price_buy_users_rows': 0, 'price_sell': 10120, 'price_sell_min': 10120, 'price_sell_min_week': 0, 'price_sell_min_month': 10120, 'price_sell_max': 10120, 'price_sell_max_week': 0, 'price_sell_max_month': 10120, 'price_sell_avg': 10120, 'price_sell_avg_week': 0, 'price_sell_avg_month': 10120, 'price_sell_users': 0, 'price_sell_users_rows': 0, 'scu_buy': 0, 'scu_buy_min': 0, 'scu_buy_min_week': 0, 'scu_buy_min_month': 0, 'scu_buy_max': 0, 'scu_buy_max_week': 0, 'scu_buy_max_month': 0, 'scu_buy_avg': 0, 'scu_buy_avg_week': 0, 'scu_buy_avg_month': 0, 'scu_buy_users': 0, 'scu_buy_users_rows': 0, 'scu_sell_stock': 72, 'scu_sell_stock_avg': 0, 'scu_sell_stock_avg_week': 0, 'scu_sell_stock_avg_month': 0, 'scu_sell': 0, 'scu_sell_min': 0, 'scu_sell_min_week': 0, 'scu_sell_min_month': 0, 'scu_sell_max': 0, 'scu_sell_max_week': 0, 'scu_sell_max_month': 0, 'scu_sell_avg': 0, 'scu_sell_avg_week': 0, 'scu_sell_avg_month': 0, 'scu_sell_users': 0, 'scu_sell_users_rows': 0, 'status_buy': 0, 'status_buy_min': 0, 'status_buy_min_week': 0, 'status_buy_min_month': 0, 'status_buy_max': 0, 'status_buy_max_week': 0, 'status_buy_max_month': 0, 'status_buy_avg': 0, 'status_buy_avg_week': 0, 'status_buy_avg_month': 0, 'status_sell': 1, 'status_sell_min': 1, 'status_sell_min_week': 0, 'status_sell_min_month': 1, 'status_sell_max': 1, 'status_sell_max_week': 0, 'status_sell_max_month': 1, 'status_sell_avg': 1, 'status_sell_avg_week': 0, 'status_sell_avg_month': 1, 'volatility_buy': 0, 'volatility_sell': 0, 'volatility_price_buy': 0, 'volatility_price_sell': 0, 'volatility_scu_buy': 0, 'volatility_scu_sell': 0, 'faction_affinity': 0, 'container_sizes': '1,2,4,8,16,24', 'game_version': '4.2', 'date_added': 1703907500, 'date_modified': 1751236644, 'commodity_name': 'Amioshi Plague', 'commodity_code': 'AMIP', 'commodity_slug': 'amioshi-plague', 'star_system_name': 'Stanton', 'planet_name': 'MicroTech', 'orbit_name': 'MicroTech', 'moon_name': 'Clio', 'space_station_name': None, 'city_name': None, 'outpost_name': 'Rayari McGrath Research Outpost', 'poi_name': None, 'faction_name': 'United Empire of Earth', 'terminal_name': 'Rayari McGrath Research Outpost', 'terminal_slug': 'rayari-mcgrath-research-outpost', 'terminal_code': 'MCGRA', 'terminal_mcs': 0, 'terminal_is_player_owned': 0}
2025-07-14 02:38:39,792 [INFO] Commodity Amioshi Plague: 0 buy locations, 0 sell locations
2025-07-14 02:38:39,792 [INFO] Generated 0 total routes
2025-07-14 02:40:06,642 [INFO] Loaded cog: applytoaegis.py
2025-07-14 02:40:06,644 [INFO] Loaded cog: embedgen.py
2025-07-14 02:40:06,645 [INFO] Loaded cog: fleetdata.py
2025-07-14 02:40:06,647 [INFO] Loaded cog: giveaway.py
2025-07-14 02:40:06,650 [INFO] Loaded cog: moderation.py
2025-07-14 02:40:06,651 [INFO] Loaded cog: rolerequest.py
2025-07-14 02:40:06,653 [INFO] Loaded cog: rosters.py
2025-07-14 02:40:06,654 [INFO] Loaded cog: shipgame.py
2025-07-14 02:40:06,655 [INFO] Loaded cog: storagetracker.py
2025-07-14 02:40:17,703 [INFO] UEX cache refreshed successfully
2025-07-14 02:40:17,703 [INFO] Loaded cog: uex_trade.py
2025-07-14 02:40:17,705 [INFO] Loaded cog: voice.py
2025-07-14 02:40:17,709 [INFO] Loaded cog: voicetracker.py
2025-07-14 02:40:17,710 [INFO] Loaded cog: welcome.py
2025-07-14 02:40:17,710 [INFO] logging in using static token
2025-07-14 02:40:18,419 [INFO] Shard ID None has connected to Gateway (Session ID: eae97b6f04e722868eca733eb49ab250).
2025-07-14 02:40:20,430 [INFO] Logged in as Aegis Nox Bot
2025-07-14 02:40:20,675 [INFO] Synced 29 slash commands globally.
2025-07-14 02:40:20,675 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 02:40:34,197 [INFO] Found 74 tradeable commodities
2025-07-14 02:40:34,197 [INFO] Commodity prices cache has 169 entries
2025-07-14 02:40:34,210 [INFO] Generated 7110 total routes
2025-07-14 02:40:34,212 [INFO] Top route: Osoian Hides - Profit: 2847852
2025-07-14 02:47:49,036 [INFO] Loaded cog: applytoaegis.py
2025-07-14 02:47:49,039 [INFO] Loaded cog: embedgen.py
2025-07-14 02:47:49,045 [INFO] Loaded cog: fleetdata.py
2025-07-14 02:47:49,049 [INFO] Loaded cog: giveaway.py
2025-07-14 02:47:49,056 [INFO] Loaded cog: moderation.py
2025-07-14 02:47:49,057 [INFO] Loaded cog: rolerequest.py
2025-07-14 02:47:49,059 [INFO] Loaded cog: rosters.py
2025-07-14 02:47:49,066 [INFO] Loaded cog: shipgame.py
2025-07-14 02:47:49,068 [INFO] Loaded cog: storagetracker.py
2025-07-14 02:48:00,919 [INFO] UEX cache refreshed successfully
2025-07-14 02:48:00,920 [INFO] Loaded cog: uex_trade.py
2025-07-14 02:48:00,924 [INFO] Loaded cog: voice.py
2025-07-14 02:48:00,933 [INFO] Loaded cog: voicetracker.py
2025-07-14 02:48:00,934 [INFO] Loaded cog: welcome.py
2025-07-14 02:48:00,936 [INFO] logging in using static token
2025-07-14 02:48:01,705 [INFO] Shard ID None has connected to Gateway (Session ID: 3c2da2f451292fdd91db36af3dd77ede).
2025-07-14 02:48:03,724 [INFO] Logged in as Aegis Nox Bot
2025-07-14 02:48:03,922 [INFO] Synced 29 slash commands globally.
2025-07-14 02:48:03,923 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 02:49:06,506 [INFO] Loaded cog: applytoaegis.py
2025-07-14 02:49:06,514 [INFO] Loaded cog: embedgen.py
2025-07-14 02:49:06,518 [INFO] Loaded cog: fleetdata.py
2025-07-14 02:49:06,522 [INFO] Loaded cog: giveaway.py
2025-07-14 02:49:06,529 [INFO] Loaded cog: moderation.py
2025-07-14 02:49:06,534 [INFO] Loaded cog: rolerequest.py
2025-07-14 02:49:06,535 [INFO] Loaded cog: rosters.py
2025-07-14 02:49:06,537 [INFO] Loaded cog: shipgame.py
2025-07-14 02:49:06,538 [INFO] Loaded cog: storagetracker.py
2025-07-14 02:49:18,442 [INFO] UEX cache refreshed successfully
2025-07-14 02:49:18,442 [INFO] Loaded cog: uex_trade.py
2025-07-14 02:49:18,445 [INFO] Loaded cog: voice.py
2025-07-14 02:49:18,450 [INFO] Loaded cog: voicetracker.py
2025-07-14 02:49:18,450 [INFO] Loaded cog: welcome.py
2025-07-14 02:49:18,451 [INFO] logging in using static token
2025-07-14 02:49:19,247 [INFO] Shard ID None has connected to Gateway (Session ID: 6cc14cd9b58ce0e1fa0085647bef9b1a).
2025-07-14 02:49:21,261 [INFO] Logged in as Aegis Nox Bot
2025-07-14 02:49:21,444 [INFO] Synced 29 slash commands globally.
2025-07-14 02:49:21,445 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 02:49:53,433 [WARNING] Interaction expired while processing traderoutes command
2025-07-14 02:49:53,494 [INFO] Found 74 tradeable commodities
2025-07-14 02:49:53,494 [INFO] Commodity prices cache has 169 entries
2025-07-14 02:49:53,558 [INFO] Generated 7110 total routes
2025-07-14 02:49:53,560 [INFO] Top route: Osoian Hides - Profit: 2847852
2025-07-14 02:50:11,579 [WARNING] Interaction expired while processing traderoutes command
2025-07-14 02:50:11,631 [INFO] Found 73 tradeable commodities
2025-07-14 02:50:11,631 [INFO] Commodity prices cache has 169 entries
2025-07-14 02:50:11,675 [INFO] Generated 7109 total routes
2025-07-14 02:50:11,678 [INFO] Top route: Bioplastic - Profit: 1100709
2025-07-14 02:53:20,157 [INFO] Found 73 tradeable commodities
2025-07-14 02:53:20,158 [INFO] Commodity prices cache has 169 entries
2025-07-14 02:53:20,248 [INFO] Generated 7109 total routes
2025-07-14 02:53:20,252 [INFO] Top route: Waste - Profit: 5333328
2025-07-14 02:55:57,886 [INFO] Found 73 tradeable commodities
2025-07-14 02:55:57,886 [INFO] Commodity prices cache has 169 entries
2025-07-14 02:55:57,939 [INFO] Generated 7109 total routes
2025-07-14 02:55:57,946 [INFO] Top route: Waste - Profit: 5333328
2025-07-14 03:02:19,762 [INFO] Loaded cog: applytoaegis.py
2025-07-14 03:02:19,765 [INFO] Loaded cog: embedgen.py
2025-07-14 03:02:19,766 [INFO] Loaded cog: fleetdata.py
2025-07-14 03:02:19,769 [INFO] Loaded cog: giveaway.py
2025-07-14 03:02:19,774 [INFO] Loaded cog: moderation.py
2025-07-14 03:02:19,776 [INFO] Loaded cog: rolerequest.py
2025-07-14 03:02:19,778 [INFO] Loaded cog: rosters.py
2025-07-14 03:02:19,780 [INFO] Loaded cog: shipgame.py
2025-07-14 03:02:19,781 [INFO] Loaded cog: storagetracker.py
2025-07-14 03:02:31,291 [INFO] UEX cache refreshed successfully
2025-07-14 03:02:31,297 [INFO] Loaded cog: uex_trade.py
2025-07-14 03:02:31,306 [INFO] Loaded cog: voice.py
2025-07-14 03:02:31,327 [INFO] Loaded cog: voicetracker.py
2025-07-14 03:02:31,345 [INFO] Loaded cog: welcome.py
2025-07-14 03:02:31,347 [INFO] logging in using static token
2025-07-14 03:02:32,283 [INFO] Shard ID None has connected to Gateway (Session ID: 0f2c55be6eb2372ca4915e4fc02835fb).
2025-07-14 03:02:34,288 [INFO] Logged in as Aegis Nox Bot
2025-07-14 03:02:34,688 [INFO] Synced 29 slash commands globally.
2025-07-14 03:02:34,690 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 03:04:01,766 [INFO] Loaded cog: applytoaegis.py
2025-07-14 03:04:01,769 [INFO] Loaded cog: embedgen.py
2025-07-14 03:04:01,772 [INFO] Loaded cog: fleetdata.py
2025-07-14 03:04:01,776 [INFO] Loaded cog: giveaway.py
2025-07-14 03:04:01,788 [INFO] Loaded cog: moderation.py
2025-07-14 03:04:01,789 [INFO] Loaded cog: rolerequest.py
2025-07-14 03:04:01,795 [INFO] Loaded cog: rosters.py
2025-07-14 03:04:01,797 [INFO] Loaded cog: shipgame.py
2025-07-14 03:04:01,798 [INFO] Loaded cog: storagetracker.py
2025-07-14 03:04:13,516 [INFO] UEX cache refreshed successfully
2025-07-14 03:04:13,516 [INFO] Loaded cog: uex_trade.py
2025-07-14 03:04:13,519 [INFO] Loaded cog: voice.py
2025-07-14 03:04:13,529 [INFO] Loaded cog: voicetracker.py
2025-07-14 03:04:13,531 [INFO] Loaded cog: welcome.py
2025-07-14 03:04:13,531 [INFO] logging in using static token
2025-07-14 03:04:14,504 [INFO] Shard ID None has connected to Gateway (Session ID: 0aea10f4b2ec0ca66b967c6e0a15857a).
2025-07-14 03:04:16,522 [INFO] Logged in as Aegis Nox Bot
2025-07-14 03:04:16,801 [INFO] Synced 29 slash commands globally.
2025-07-14 03:04:16,802 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 03:05:22,176 [INFO] Loaded cog: applytoaegis.py
2025-07-14 03:05:22,178 [INFO] Loaded cog: embedgen.py
2025-07-14 03:05:22,179 [INFO] Loaded cog: fleetdata.py
2025-07-14 03:05:22,186 [INFO] Loaded cog: giveaway.py
2025-07-14 03:05:22,189 [INFO] Loaded cog: moderation.py
2025-07-14 03:05:22,190 [INFO] Loaded cog: rolerequest.py
2025-07-14 03:05:22,191 [INFO] Loaded cog: rosters.py
2025-07-14 03:05:22,193 [INFO] Loaded cog: shipgame.py
2025-07-14 03:05:22,194 [INFO] Loaded cog: storagetracker.py
2025-07-14 03:05:33,773 [INFO] UEX cache refreshed successfully
2025-07-14 03:05:33,774 [INFO] Loaded cog: uex_trade.py
2025-07-14 03:05:33,775 [INFO] Loaded cog: voice.py
2025-07-14 03:05:33,781 [INFO] Loaded cog: voicetracker.py
2025-07-14 03:05:33,786 [INFO] Loaded cog: welcome.py
2025-07-14 03:05:33,786 [INFO] logging in using static token
2025-07-14 03:05:34,469 [INFO] Shard ID None has connected to Gateway (Session ID: 245f12d1cd89225900fae4e0256b9f2a).
2025-07-14 03:05:36,484 [INFO] Logged in as Aegis Nox Bot
2025-07-14 03:05:37,043 [INFO] Synced 29 slash commands globally.
2025-07-14 03:05:37,044 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 03:06:49,532 [INFO] Loaded cog: applytoaegis.py
2025-07-14 03:06:49,537 [INFO] Loaded cog: embedgen.py
2025-07-14 03:06:49,539 [INFO] Loaded cog: fleetdata.py
2025-07-14 03:06:49,545 [INFO] Loaded cog: giveaway.py
2025-07-14 03:06:49,551 [INFO] Loaded cog: moderation.py
2025-07-14 03:06:49,555 [INFO] Loaded cog: rolerequest.py
2025-07-14 03:06:49,560 [INFO] Loaded cog: rosters.py
2025-07-14 03:06:49,562 [INFO] Loaded cog: shipgame.py
2025-07-14 03:06:49,564 [INFO] Loaded cog: storagetracker.py
2025-07-14 03:07:00,977 [INFO] UEX cache refreshed successfully
2025-07-14 03:07:00,988 [INFO] Loaded cog: uex_trade.py
2025-07-14 03:07:00,992 [INFO] Loaded cog: voice.py
2025-07-14 03:07:00,998 [INFO] Loaded cog: voicetracker.py
2025-07-14 03:07:00,999 [INFO] Loaded cog: welcome.py
2025-07-14 03:07:00,999 [INFO] logging in using static token
2025-07-14 03:07:01,863 [INFO] Shard ID None has connected to Gateway (Session ID: 3c330531f7b5a85ce7a1a43f5df7548f).
2025-07-14 03:07:03,890 [INFO] Logged in as Aegis Nox Bot
2025-07-14 03:07:04,174 [INFO] Synced 29 slash commands globally.
2025-07-14 03:07:04,175 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 03:12:12,048 [WARNING] Interaction expired while processing traderoutes command
2025-07-14 03:12:12,075 [INFO] Found 73 tradeable commodities
2025-07-14 03:12:12,078 [INFO] Commodity prices cache has 169 entries
2025-07-14 03:12:12,091 [INFO] High profit route: E'tam - Buy: 7143, Sell: 10739, SCU: 279, Profit: 1003284, Investment: 1992897
2025-07-14 03:12:12,125 [INFO] High profit route: Bioplastic - Buy: 5242, Sell: 8131, SCU: 381, Profit: 1100709, Investment: 1997202
2025-07-14 03:12:12,129 [INFO] High profit route: Bioplastic - Buy: 5242, Sell: 7997, SCU: 381, Profit: 1049655, Investment: 1997202
2025-07-14 03:12:12,130 [INFO] High profit route: Bioplastic - Buy: 5242, Sell: 7929, SCU: 381, Profit: 1023747, Investment: 1997202
2025-07-14 03:12:12,130 [INFO] High profit route: Bioplastic - Buy: 5355, Sell: 8131, SCU: 373, Profit: 1035448, Investment: 1997415
2025-07-14 03:12:12,131 [INFO] High profit route: Bioplastic - Buy: 5359, Sell: 8131, SCU: 373, Profit: 1033956, Investment: 1998907
2025-07-14 03:12:12,136 [INFO] Generated 7109 total routes
2025-07-14 03:12:12,140 [INFO] Top route: Bioplastic - Profit: 1100709
2025-07-14 03:20:18,215 [INFO] Loaded cog: applytoaegis.py
2025-07-14 03:20:18,225 [INFO] Loaded cog: embedgen.py
2025-07-14 03:20:18,238 [INFO] Loaded cog: fleetdata.py
2025-07-14 03:20:18,241 [INFO] Loaded cog: giveaway.py
2025-07-14 03:20:18,259 [INFO] Loaded cog: moderation.py
2025-07-14 03:20:18,267 [INFO] Loaded cog: rolerequest.py
2025-07-14 03:20:18,271 [INFO] Loaded cog: rosters.py
2025-07-14 03:20:18,273 [INFO] Loaded cog: shipgame.py
2025-07-14 03:20:18,285 [INFO] Loaded cog: storagetracker.py
2025-07-14 03:20:30,750 [INFO] UEX cache refreshed successfully
2025-07-14 03:20:30,750 [INFO] Loaded cog: uex_trade.py
2025-07-14 03:20:30,762 [INFO] Loaded cog: voice.py
2025-07-14 03:20:30,791 [INFO] Loaded cog: voicetracker.py
2025-07-14 03:20:30,798 [INFO] Loaded cog: welcome.py
2025-07-14 03:20:30,798 [INFO] logging in using static token
2025-07-14 03:20:31,590 [INFO] Shard ID None has connected to Gateway (Session ID: b13fe2fa97454de176123db3ed46efc0).
2025-07-14 03:20:33,626 [INFO] Logged in as Aegis Nox Bot
2025-07-14 03:20:34,691 [INFO] Synced 29 slash commands globally.
2025-07-14 03:20:34,691 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 03:21:49,754 [INFO] Loaded cog: applytoaegis.py
2025-07-14 03:21:49,759 [INFO] Loaded cog: embedgen.py
2025-07-14 03:21:49,769 [INFO] Loaded cog: fleetdata.py
2025-07-14 03:21:49,772 [INFO] Loaded cog: giveaway.py
2025-07-14 03:21:49,782 [INFO] Loaded cog: moderation.py
2025-07-14 03:21:49,786 [INFO] Loaded cog: rolerequest.py
2025-07-14 03:21:49,796 [INFO] Loaded cog: rosters.py
2025-07-14 03:21:49,798 [INFO] Loaded cog: shipgame.py
2025-07-14 03:21:49,800 [INFO] Loaded cog: storagetracker.py
2025-07-14 03:22:01,850 [INFO] UEX cache refreshed successfully
2025-07-14 03:22:01,853 [INFO] Loaded cog: uex_trade.py
2025-07-14 03:22:01,857 [INFO] Loaded cog: voice.py
2025-07-14 03:22:01,886 [INFO] Loaded cog: voicetracker.py
2025-07-14 03:22:01,889 [INFO] Loaded cog: welcome.py
2025-07-14 03:22:01,900 [INFO] logging in using static token
2025-07-14 03:22:02,725 [INFO] Shard ID None has connected to Gateway (Session ID: 2efde6893dd4ab0795596a7d48a0e1eb).
2025-07-14 03:22:04,741 [INFO] Logged in as Aegis Nox Bot
2025-07-14 03:22:04,949 [INFO] Synced 29 slash commands globally.
2025-07-14 03:22:04,956 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 03:24:42,332 [INFO] Loaded cog: applytoaegis.py
2025-07-14 03:24:42,335 [INFO] Loaded cog: embedgen.py
2025-07-14 03:24:42,342 [INFO] Loaded cog: fleetdata.py
2025-07-14 03:24:42,361 [INFO] Loaded cog: giveaway.py
2025-07-14 03:24:42,389 [INFO] Loaded cog: moderation.py
2025-07-14 03:24:42,400 [INFO] Loaded cog: rolerequest.py
2025-07-14 03:24:42,415 [INFO] Loaded cog: rosters.py
2025-07-14 03:24:42,419 [INFO] Loaded cog: shipgame.py
2025-07-14 03:24:42,420 [INFO] Loaded cog: storagetracker.py
2025-07-14 03:24:54,210 [INFO] UEX cache refreshed successfully
2025-07-14 03:24:54,210 [INFO] Loaded cog: uex_trade.py
2025-07-14 03:24:54,216 [INFO] Loaded cog: voice.py
2025-07-14 03:24:54,231 [INFO] Loaded cog: voicetracker.py
2025-07-14 03:24:54,232 [INFO] Loaded cog: welcome.py
2025-07-14 03:24:54,233 [INFO] logging in using static token
2025-07-14 03:24:55,011 [INFO] Shard ID None has connected to Gateway (Session ID: 3ee04533794a5a7d056c03bf88bb8eaa).
2025-07-14 03:24:57,047 [INFO] Logged in as Aegis Nox Bot
2025-07-14 03:24:57,623 [INFO] Synced 30 slash commands globally.
2025-07-14 03:24:57,624 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'testinvestment', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 03:25:50,658 [WARNING] Interaction expired while processing traderoutes command
2025-07-14 03:25:50,677 [WARNING] Interaction expired while processing traderoutes command
2025-07-14 03:25:50,749 [INFO] Found 73 tradeable commodities
2025-07-14 03:25:50,752 [INFO] Commodity prices cache has 169 entries
2025-07-14 03:25:50,772 [INFO] High profit route: E'tam - Buy: 7143, Sell: 10739, SCU: 279, Profit: 1003284, Investment: 1992897
2025-07-14 03:25:50,803 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=6858
2025-07-14 03:25:50,806 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,806 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,814 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=6858
2025-07-14 03:25:50,816 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,816 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,817 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=6858
2025-07-14 03:25:50,817 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,818 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,818 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=6632
2025-07-14 03:25:50,818 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,818 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,819 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7229
2025-07-14 03:25:50,819 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,820 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,820 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=8131
2025-07-14 03:25:50,821 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,821 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,822 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7100
2025-07-14 03:25:50,822 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,822 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,822 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7164
2025-07-14 03:25:50,823 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,823 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,823 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7997
2025-07-14 03:25:50,823 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,823 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,823 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7052
2025-07-14 03:25:50,823 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,825 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,825 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7052
2025-07-14 03:25:50,825 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,825 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,826 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7051
2025-07-14 03:25:50,826 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,826 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,826 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=6866
2025-07-14 03:25:50,826 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,827 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,827 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=6833
2025-07-14 03:25:50,827 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,827 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,827 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=6858
2025-07-14 03:25:50,827 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,828 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,828 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7052
2025-07-14 03:25:50,829 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,830 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,830 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=6099
2025-07-14 03:25:50,830 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,830 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,842 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7929
2025-07-14 03:25:50,843 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,844 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,844 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7229
2025-07-14 03:25:50,844 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,844 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,845 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=6971
2025-07-14 03:25:50,845 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,845 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,845 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7644
2025-07-14 03:25:50,845 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,846 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,846 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7229
2025-07-14 03:25:50,846 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:50,847 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:50,847 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=6858
2025-07-14 03:25:50,847 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,847 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,847 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=6858
2025-07-14 03:25:50,848 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,848 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,848 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=6858
2025-07-14 03:25:50,848 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,849 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,849 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=6632
2025-07-14 03:25:50,850 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,850 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,851 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7229
2025-07-14 03:25:50,858 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,860 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,861 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=8131
2025-07-14 03:25:50,861 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,861 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,861 [INFO] High profit route: Bioplastic - Buy: 5242, Sell: 8131, SCU: 381, Profit: 1100709, Investment: 1997202
2025-07-14 03:25:50,861 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7100
2025-07-14 03:25:50,862 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,862 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,863 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7164
2025-07-14 03:25:50,864 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,864 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,866 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7997
2025-07-14 03:25:50,866 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,866 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,867 [INFO] High profit route: Bioplastic - Buy: 5242, Sell: 7997, SCU: 381, Profit: 1049655, Investment: 1997202
2025-07-14 03:25:50,867 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7052
2025-07-14 03:25:50,867 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,867 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,867 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7052
2025-07-14 03:25:50,867 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,868 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,868 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7051
2025-07-14 03:25:50,868 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,868 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,868 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=6866
2025-07-14 03:25:50,868 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,869 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,869 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=6833
2025-07-14 03:25:50,869 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,869 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,869 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=6858
2025-07-14 03:25:50,870 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,870 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,870 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7052
2025-07-14 03:25:50,870 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,870 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,871 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=6099
2025-07-14 03:25:50,871 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,872 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,872 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7929
2025-07-14 03:25:50,872 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,872 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,873 [INFO] High profit route: Bioplastic - Buy: 5242, Sell: 7929, SCU: 381, Profit: 1023747, Investment: 1997202
2025-07-14 03:25:50,882 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7229
2025-07-14 03:25:50,882 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,883 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,883 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=6971
2025-07-14 03:25:50,884 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,884 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,889 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7644
2025-07-14 03:25:50,889 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,889 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,889 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7229
2025-07-14 03:25:50,890 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:50,890 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:50,890 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=6858
2025-07-14 03:25:50,890 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,890 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,891 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=6858
2025-07-14 03:25:50,891 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,891 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,891 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=6858
2025-07-14 03:25:50,891 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,891 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,891 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=6632
2025-07-14 03:25:50,892 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,892 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,892 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7229
2025-07-14 03:25:50,892 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,892 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,892 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=8131
2025-07-14 03:25:50,893 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,894 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,894 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7100
2025-07-14 03:25:50,894 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,894 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,895 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7164
2025-07-14 03:25:50,895 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,904 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,904 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7997
2025-07-14 03:25:50,904 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,905 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,905 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7052
2025-07-14 03:25:50,905 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,905 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,906 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7052
2025-07-14 03:25:50,906 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,908 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,910 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7051
2025-07-14 03:25:50,911 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,911 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,911 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=6866
2025-07-14 03:25:50,911 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,911 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,912 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=6833
2025-07-14 03:25:50,912 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,912 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,912 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=6858
2025-07-14 03:25:50,912 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,913 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,913 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7052
2025-07-14 03:25:50,913 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,913 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,914 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=6099
2025-07-14 03:25:50,914 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7929
2025-07-14 03:25:50,914 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,914 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,915 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7229
2025-07-14 03:25:50,915 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,915 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,916 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=6971
2025-07-14 03:25:50,924 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,925 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,925 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7644
2025-07-14 03:25:50,925 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,926 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,927 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7229
2025-07-14 03:25:50,927 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:50,928 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:50,929 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=6858
2025-07-14 03:25:50,931 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,931 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,931 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=6858
2025-07-14 03:25:50,931 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,932 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,932 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=6858
2025-07-14 03:25:50,932 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,932 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,933 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=6632
2025-07-14 03:25:50,933 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,933 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,933 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7229
2025-07-14 03:25:50,933 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,933 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,933 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=8131
2025-07-14 03:25:50,934 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,934 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,934 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7100
2025-07-14 03:25:50,934 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,934 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,935 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7164
2025-07-14 03:25:50,935 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,935 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,935 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7997
2025-07-14 03:25:50,936 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,936 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,936 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7052
2025-07-14 03:25:50,936 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,937 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,937 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7052
2025-07-14 03:25:50,937 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,937 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,938 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7051
2025-07-14 03:25:50,939 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,939 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,939 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=6866
2025-07-14 03:25:50,939 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,940 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,941 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=6833
2025-07-14 03:25:50,948 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,949 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,949 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=6858
2025-07-14 03:25:50,949 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,950 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,950 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7052
2025-07-14 03:25:50,950 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,950 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,950 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=6099
2025-07-14 03:25:50,952 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7929
2025-07-14 03:25:50,952 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,952 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,954 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7229
2025-07-14 03:25:50,954 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,954 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,955 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=6971
2025-07-14 03:25:50,955 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,955 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,955 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7644
2025-07-14 03:25:50,955 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,955 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,956 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7229
2025-07-14 03:25:50,956 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:50,956 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:50,957 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=6858
2025-07-14 03:25:50,957 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:50,957 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:50,957 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=6858
2025-07-14 03:25:50,957 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:50,958 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:50,958 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=6858
2025-07-14 03:25:50,958 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:50,958 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:50,958 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=6632
2025-07-14 03:25:50,958 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:50,958 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:50,959 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7229
2025-07-14 03:25:50,959 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:50,959 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:50,959 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=8131
2025-07-14 03:25:50,960 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:50,960 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:50,961 [INFO] High profit route: Bioplastic - Buy: 5355, Sell: 8131, SCU: 373, Profit: 1035448, Investment: 1997415
2025-07-14 03:25:50,961 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7100
2025-07-14 03:25:50,961 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:50,962 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:50,962 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7164
2025-07-14 03:25:50,971 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:50,971 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:50,972 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7997
2025-07-14 03:25:50,972 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:50,972 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:50,972 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7052
2025-07-14 03:25:50,973 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:50,973 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:50,973 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7052
2025-07-14 03:25:50,973 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:50,975 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:50,979 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7051
2025-07-14 03:25:50,979 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:50,979 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:50,980 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=6866
2025-07-14 03:25:50,980 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:50,980 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:50,981 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=6833
2025-07-14 03:25:50,981 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:50,981 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:50,981 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=6858
2025-07-14 03:25:50,981 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:50,981 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:50,981 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7052
2025-07-14 03:25:50,982 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:50,983 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:50,983 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=6099
2025-07-14 03:25:50,993 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:50,994 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:50,994 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7929
2025-07-14 03:25:50,994 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:50,996 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:50,998 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7229
2025-07-14 03:25:50,999 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:50,999 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:50,999 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=6971
2025-07-14 03:25:51,000 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,000 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:51,000 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7644
2025-07-14 03:25:51,000 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,000 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:51,000 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7229
2025-07-14 03:25:51,001 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,001 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:51,001 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=6858
2025-07-14 03:25:51,002 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,002 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,002 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=6858
2025-07-14 03:25:51,002 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,002 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,002 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=6858
2025-07-14 03:25:51,003 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,003 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,003 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=6632
2025-07-14 03:25:51,003 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,004 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,004 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7229
2025-07-14 03:25:51,004 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,004 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,005 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=8131
2025-07-14 03:25:51,005 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,005 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,006 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7100
2025-07-14 03:25:51,006 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,006 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,006 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7164
2025-07-14 03:25:51,006 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,007 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,008 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7997
2025-07-14 03:25:51,008 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,008 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,008 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7052
2025-07-14 03:25:51,009 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,009 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,009 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7052
2025-07-14 03:25:51,009 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,009 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,009 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7051
2025-07-14 03:25:51,010 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,010 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,010 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=6866
2025-07-14 03:25:51,010 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,010 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,010 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=6833
2025-07-14 03:25:51,010 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,011 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,011 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=6858
2025-07-14 03:25:51,011 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,011 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,011 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7052
2025-07-14 03:25:51,011 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,012 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,012 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=6099
2025-07-14 03:25:51,012 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7929
2025-07-14 03:25:51,012 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,012 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,012 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7229
2025-07-14 03:25:51,013 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,013 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,013 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=6971
2025-07-14 03:25:51,013 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,013 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,013 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7644
2025-07-14 03:25:51,013 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,014 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,014 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7229
2025-07-14 03:25:51,014 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:51,014 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:51,014 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=6858
2025-07-14 03:25:51,014 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,015 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,015 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=6858
2025-07-14 03:25:51,015 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,017 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,018 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=6858
2025-07-14 03:25:51,018 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,018 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,027 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=6632
2025-07-14 03:25:51,027 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,028 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,028 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7229
2025-07-14 03:25:51,029 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,029 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,029 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=8131
2025-07-14 03:25:51,030 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,030 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,031 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7100
2025-07-14 03:25:51,031 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,032 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,033 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7164
2025-07-14 03:25:51,034 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,035 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,045 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7997
2025-07-14 03:25:51,048 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,049 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,049 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7052
2025-07-14 03:25:51,049 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,049 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,051 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7052
2025-07-14 03:25:51,051 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,055 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,055 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7051
2025-07-14 03:25:51,055 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,055 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,055 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=6866
2025-07-14 03:25:51,056 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,056 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,056 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=6833
2025-07-14 03:25:51,058 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,058 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,069 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=6858
2025-07-14 03:25:51,071 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,071 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,071 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7052
2025-07-14 03:25:51,072 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,072 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,072 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=6099
2025-07-14 03:25:51,072 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7929
2025-07-14 03:25:51,072 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,078 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,080 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7229
2025-07-14 03:25:51,080 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,088 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,091 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=6971
2025-07-14 03:25:51,091 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,092 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,092 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7644
2025-07-14 03:25:51,092 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,092 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,092 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7229
2025-07-14 03:25:51,092 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:51,093 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:51,094 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=6858
2025-07-14 03:25:51,094 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,094 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,095 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=6858
2025-07-14 03:25:51,095 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,096 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,097 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=6858
2025-07-14 03:25:51,097 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,098 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,099 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=6632
2025-07-14 03:25:51,099 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,100 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,100 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7229
2025-07-14 03:25:51,100 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,100 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,101 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=8131
2025-07-14 03:25:51,101 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,114 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,115 [INFO] High profit route: Bioplastic - Buy: 5359, Sell: 8131, SCU: 373, Profit: 1033956, Investment: 1998907
2025-07-14 03:25:51,115 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7100
2025-07-14 03:25:51,120 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,121 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,121 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7164
2025-07-14 03:25:51,121 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,122 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,122 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7997
2025-07-14 03:25:51,122 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,123 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,124 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7052
2025-07-14 03:25:51,124 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,131 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,132 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7052
2025-07-14 03:25:51,132 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,132 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,135 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7051
2025-07-14 03:25:51,136 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,136 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,136 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=6866
2025-07-14 03:25:51,137 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,138 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,138 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=6833
2025-07-14 03:25:51,138 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,140 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,141 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=6858
2025-07-14 03:25:51,142 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,143 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,143 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7052
2025-07-14 03:25:51,143 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,143 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,143 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=6099
2025-07-14 03:25:51,144 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,144 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,144 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7929
2025-07-14 03:25:51,145 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,145 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,146 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7229
2025-07-14 03:25:51,146 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,153 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,155 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=6971
2025-07-14 03:25:51,155 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,156 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,157 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7644
2025-07-14 03:25:51,157 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,158 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,158 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7229
2025-07-14 03:25:51,158 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:51,160 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:51,163 [INFO] Generated 7109 total routes
2025-07-14 03:25:51,165 [INFO] Top route: Bioplastic - Profit: 1100709
2025-07-14 03:25:59,491 [INFO] Found 73 tradeable commodities
2025-07-14 03:25:59,491 [INFO] Commodity prices cache has 169 entries
2025-07-14 03:25:59,558 [INFO] High profit route: E'tam - Buy: 7143, Sell: 10739, SCU: 279, Profit: 1003284, Investment: 1992897
2025-07-14 03:25:59,596 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=6858
2025-07-14 03:25:59,598 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,599 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,599 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=6858
2025-07-14 03:25:59,599 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,600 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,600 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=6858
2025-07-14 03:25:59,600 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,600 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,600 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=6632
2025-07-14 03:25:59,600 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,600 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,601 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7229
2025-07-14 03:25:59,601 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,601 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,601 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=8131
2025-07-14 03:25:59,601 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,601 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,602 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7100
2025-07-14 03:25:59,602 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,602 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,602 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7164
2025-07-14 03:25:59,602 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,602 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,602 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7997
2025-07-14 03:25:59,603 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,603 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,603 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7052
2025-07-14 03:25:59,604 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,604 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,604 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7052
2025-07-14 03:25:59,604 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,605 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,605 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7051
2025-07-14 03:25:59,605 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,605 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,617 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=6866
2025-07-14 03:25:59,617 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,618 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,618 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=6833
2025-07-14 03:25:59,618 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,619 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,619 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=6858
2025-07-14 03:25:59,620 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,620 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,620 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7052
2025-07-14 03:25:59,623 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,624 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,624 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=6099
2025-07-14 03:25:59,624 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,625 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,625 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7929
2025-07-14 03:25:59,625 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,625 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,625 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7229
2025-07-14 03:25:59,626 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,626 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,626 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=6971
2025-07-14 03:25:59,627 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,627 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,628 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7644
2025-07-14 03:25:59,629 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,629 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,630 [INFO] Price data for Bioplastic: buy_price=6070, sell_price=7229
2025-07-14 03:25:59,630 [INFO] Bioplastic calculation: buy_price=6070, investment_limit=2000000.0, max_scu_by_investment=329, vehicle_scu=388, final_max_scu=329
2025-07-14 03:25:59,630 [INFO] Bioplastic verification: max_scu=329, actual_investment=1997030, within_limit=True
2025-07-14 03:25:59,630 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=6858
2025-07-14 03:25:59,630 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,630 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,641 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=6858
2025-07-14 03:25:59,642 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,642 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,642 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=6858
2025-07-14 03:25:59,642 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,642 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,643 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=6632
2025-07-14 03:25:59,643 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,643 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,643 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7229
2025-07-14 03:25:59,644 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,645 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,645 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=8131
2025-07-14 03:25:59,645 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,646 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,647 [INFO] High profit route: Bioplastic - Buy: 5242, Sell: 8131, SCU: 381, Profit: 1100709, Investment: 1997202
2025-07-14 03:25:59,648 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7100
2025-07-14 03:25:59,649 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,649 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,649 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7164
2025-07-14 03:25:59,650 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,650 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,651 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7997
2025-07-14 03:25:59,651 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,651 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,652 [INFO] High profit route: Bioplastic - Buy: 5242, Sell: 7997, SCU: 381, Profit: 1049655, Investment: 1997202
2025-07-14 03:25:59,661 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7052
2025-07-14 03:25:59,661 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,662 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,663 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7052
2025-07-14 03:25:59,663 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,663 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,663 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7051
2025-07-14 03:25:59,663 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,664 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,664 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=6866
2025-07-14 03:25:59,665 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,665 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,665 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=6833
2025-07-14 03:25:59,666 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,667 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,667 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=6858
2025-07-14 03:25:59,668 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,668 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,668 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7052
2025-07-14 03:25:59,668 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,668 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,669 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=6099
2025-07-14 03:25:59,669 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,669 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,669 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7929
2025-07-14 03:25:59,669 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,669 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,670 [INFO] High profit route: Bioplastic - Buy: 5242, Sell: 7929, SCU: 381, Profit: 1023747, Investment: 1997202
2025-07-14 03:25:59,670 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7229
2025-07-14 03:25:59,670 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,670 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,670 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=6971
2025-07-14 03:25:59,671 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,671 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,671 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7644
2025-07-14 03:25:59,671 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,673 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,681 [INFO] Price data for Bioplastic: buy_price=5242, sell_price=7229
2025-07-14 03:25:59,684 [INFO] Bioplastic calculation: buy_price=5242, investment_limit=2000000.0, max_scu_by_investment=381, vehicle_scu=388, final_max_scu=381
2025-07-14 03:25:59,684 [INFO] Bioplastic verification: max_scu=381, actual_investment=1997202, within_limit=True
2025-07-14 03:25:59,684 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=6858
2025-07-14 03:25:59,685 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,685 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,685 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=6858
2025-07-14 03:25:59,685 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,685 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,686 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=6858
2025-07-14 03:25:59,687 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,687 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,688 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=6632
2025-07-14 03:25:59,689 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,689 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,689 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7229
2025-07-14 03:25:59,690 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,690 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,691 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=8131
2025-07-14 03:25:59,691 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,691 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,691 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7100
2025-07-14 03:25:59,691 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,692 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,692 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7164
2025-07-14 03:25:59,692 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,692 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,692 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7997
2025-07-14 03:25:59,692 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,692 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,693 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7052
2025-07-14 03:25:59,693 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,693 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,694 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7052
2025-07-14 03:25:59,694 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,700 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,706 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7051
2025-07-14 03:25:59,707 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,707 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,707 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=6866
2025-07-14 03:25:59,708 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,709 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,709 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=6833
2025-07-14 03:25:59,713 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,713 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,714 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=6858
2025-07-14 03:25:59,714 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,714 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,714 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7052
2025-07-14 03:25:59,715 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,715 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,715 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=6099
2025-07-14 03:25:59,715 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7929
2025-07-14 03:25:59,715 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,715 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,716 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7229
2025-07-14 03:25:59,717 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,717 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,718 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=6971
2025-07-14 03:25:59,718 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,718 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,719 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7644
2025-07-14 03:25:59,719 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,725 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,726 [INFO] Price data for Bioplastic: buy_price=6213, sell_price=7229
2025-07-14 03:25:59,728 [INFO] Bioplastic calculation: buy_price=6213, investment_limit=2000000.0, max_scu_by_investment=321, vehicle_scu=388, final_max_scu=321
2025-07-14 03:25:59,728 [INFO] Bioplastic verification: max_scu=321, actual_investment=1994373, within_limit=True
2025-07-14 03:25:59,728 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=6858
2025-07-14 03:25:59,729 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,729 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,729 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=6858
2025-07-14 03:25:59,729 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,729 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,730 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=6858
2025-07-14 03:25:59,730 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,730 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,730 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=6632
2025-07-14 03:25:59,730 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,732 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,732 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7229
2025-07-14 03:25:59,732 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,735 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,735 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=8131
2025-07-14 03:25:59,735 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,735 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,736 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7100
2025-07-14 03:25:59,736 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,736 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,737 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7164
2025-07-14 03:25:59,737 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,738 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,738 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7997
2025-07-14 03:25:59,738 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,738 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,738 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7052
2025-07-14 03:25:59,739 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,740 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,740 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7052
2025-07-14 03:25:59,748 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,749 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,750 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7051
2025-07-14 03:25:59,750 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,750 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,750 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=6866
2025-07-14 03:25:59,750 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,750 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,750 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=6833
2025-07-14 03:25:59,751 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,751 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,751 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=6858
2025-07-14 03:25:59,751 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,751 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,751 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7052
2025-07-14 03:25:59,753 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,753 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,753 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=6099
2025-07-14 03:25:59,754 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7929
2025-07-14 03:25:59,754 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,755 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,755 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7229
2025-07-14 03:25:59,755 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,755 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,755 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=6971
2025-07-14 03:25:59,755 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,756 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,756 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7644
2025-07-14 03:25:59,756 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,756 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,757 [INFO] Price data for Bioplastic: buy_price=6116, sell_price=7229
2025-07-14 03:25:59,757 [INFO] Bioplastic calculation: buy_price=6116, investment_limit=2000000.0, max_scu_by_investment=327, vehicle_scu=388, final_max_scu=327
2025-07-14 03:25:59,757 [INFO] Bioplastic verification: max_scu=327, actual_investment=1999932, within_limit=True
2025-07-14 03:25:59,757 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=6858
2025-07-14 03:25:59,757 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,757 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,758 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=6858
2025-07-14 03:25:59,758 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,758 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,758 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=6858
2025-07-14 03:25:59,758 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,759 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,759 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=6632
2025-07-14 03:25:59,759 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,759 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,759 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7229
2025-07-14 03:25:59,759 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,760 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,760 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=8131
2025-07-14 03:25:59,760 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,760 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,760 [INFO] High profit route: Bioplastic - Buy: 5355, Sell: 8131, SCU: 373, Profit: 1035448, Investment: 1997415
2025-07-14 03:25:59,761 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7100
2025-07-14 03:25:59,761 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,761 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,761 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7164
2025-07-14 03:25:59,762 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,768 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,771 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7997
2025-07-14 03:25:59,771 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,772 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,772 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7052
2025-07-14 03:25:59,772 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,772 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,773 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7052
2025-07-14 03:25:59,773 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,773 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,775 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7051
2025-07-14 03:25:59,775 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,775 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,777 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=6866
2025-07-14 03:25:59,777 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,777 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,777 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=6833
2025-07-14 03:25:59,778 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,778 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,778 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=6858
2025-07-14 03:25:59,778 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,778 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,778 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7052
2025-07-14 03:25:59,778 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,779 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,779 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=6099
2025-07-14 03:25:59,779 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,779 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,779 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7929
2025-07-14 03:25:59,779 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,779 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,780 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7229
2025-07-14 03:25:59,780 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,780 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,780 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=6971
2025-07-14 03:25:59,781 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,781 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,781 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7644
2025-07-14 03:25:59,781 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,781 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,781 [INFO] Price data for Bioplastic: buy_price=5355, sell_price=7229
2025-07-14 03:25:59,782 [INFO] Bioplastic calculation: buy_price=5355, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,782 [INFO] Bioplastic verification: max_scu=373, actual_investment=1997415, within_limit=True
2025-07-14 03:25:59,782 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=6858
2025-07-14 03:25:59,782 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,782 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,783 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=6858
2025-07-14 03:25:59,784 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,784 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,784 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=6858
2025-07-14 03:25:59,784 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,784 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,784 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=6632
2025-07-14 03:25:59,785 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,785 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,790 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7229
2025-07-14 03:25:59,791 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,792 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,793 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=8131
2025-07-14 03:25:59,793 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,793 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,794 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7100
2025-07-14 03:25:59,794 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,794 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,794 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7164
2025-07-14 03:25:59,794 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,794 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,794 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7997
2025-07-14 03:25:59,795 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,795 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,796 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7052
2025-07-14 03:25:59,796 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,798 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,799 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7052
2025-07-14 03:25:59,799 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,799 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,799 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7051
2025-07-14 03:25:59,799 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,800 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,800 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=6866
2025-07-14 03:25:59,800 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,800 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,800 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=6833
2025-07-14 03:25:59,801 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,801 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,801 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=6858
2025-07-14 03:25:59,801 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,801 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,801 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7052
2025-07-14 03:25:59,801 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,802 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,802 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=6099
2025-07-14 03:25:59,802 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7929
2025-07-14 03:25:59,802 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,802 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,802 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7229
2025-07-14 03:25:59,802 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,803 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,803 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=6971
2025-07-14 03:25:59,803 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,803 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,803 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7644
2025-07-14 03:25:59,804 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,804 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,804 [INFO] Price data for Bioplastic: buy_price=6288, sell_price=7229
2025-07-14 03:25:59,804 [INFO] Bioplastic calculation: buy_price=6288, investment_limit=2000000.0, max_scu_by_investment=318, vehicle_scu=388, final_max_scu=318
2025-07-14 03:25:59,804 [INFO] Bioplastic verification: max_scu=318, actual_investment=1999584, within_limit=True
2025-07-14 03:25:59,804 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=6858
2025-07-14 03:25:59,805 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,805 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,805 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=6858
2025-07-14 03:25:59,807 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,808 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,809 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=6858
2025-07-14 03:25:59,812 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,813 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,813 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=6632
2025-07-14 03:25:59,814 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,814 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,815 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7229
2025-07-14 03:25:59,816 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,816 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,816 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=8131
2025-07-14 03:25:59,816 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,817 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,817 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7100
2025-07-14 03:25:59,817 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,817 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,818 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7164
2025-07-14 03:25:59,819 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,820 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,820 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7997
2025-07-14 03:25:59,821 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,850 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,850 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7052
2025-07-14 03:25:59,850 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,851 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,855 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7052
2025-07-14 03:25:59,856 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,856 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,856 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7051
2025-07-14 03:25:59,857 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,859 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,859 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=6866
2025-07-14 03:25:59,859 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,859 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,859 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=6833
2025-07-14 03:25:59,859 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,859 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,860 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=6858
2025-07-14 03:25:59,860 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,860 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,860 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7052
2025-07-14 03:25:59,860 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,860 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,861 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=6099
2025-07-14 03:25:59,861 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7929
2025-07-14 03:25:59,862 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,862 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,864 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7229
2025-07-14 03:25:59,864 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,864 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,865 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=6971
2025-07-14 03:25:59,865 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,865 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,865 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7644
2025-07-14 03:25:59,865 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,865 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,866 [INFO] Price data for Bioplastic: buy_price=6171, sell_price=7229
2025-07-14 03:25:59,866 [INFO] Bioplastic calculation: buy_price=6171, investment_limit=2000000.0, max_scu_by_investment=324, vehicle_scu=388, final_max_scu=324
2025-07-14 03:25:59,866 [INFO] Bioplastic verification: max_scu=324, actual_investment=1999404, within_limit=True
2025-07-14 03:25:59,867 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=6858
2025-07-14 03:25:59,867 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,867 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,867 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=6858
2025-07-14 03:25:59,867 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,867 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,868 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=6858
2025-07-14 03:25:59,868 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,868 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,868 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=6632
2025-07-14 03:25:59,868 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,868 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,868 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7229
2025-07-14 03:25:59,869 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,869 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,869 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=8131
2025-07-14 03:25:59,870 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,870 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,870 [INFO] High profit route: Bioplastic - Buy: 5359, Sell: 8131, SCU: 373, Profit: 1033956, Investment: 1998907
2025-07-14 03:25:59,870 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7100
2025-07-14 03:25:59,870 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,870 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,871 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7164
2025-07-14 03:25:59,871 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,871 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,871 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7997
2025-07-14 03:25:59,871 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,871 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,872 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7052
2025-07-14 03:25:59,873 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,873 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,878 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7052
2025-07-14 03:25:59,878 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,878 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,878 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7051
2025-07-14 03:25:59,880 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,880 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,881 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=6866
2025-07-14 03:25:59,881 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,882 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,882 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=6833
2025-07-14 03:25:59,882 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,882 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,882 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=6858
2025-07-14 03:25:59,882 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,883 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,883 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7052
2025-07-14 03:25:59,884 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,885 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,886 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=6099
2025-07-14 03:25:59,887 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,887 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,887 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7929
2025-07-14 03:25:59,887 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,888 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,888 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7229
2025-07-14 03:25:59,888 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,888 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,888 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=6971
2025-07-14 03:25:59,888 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,888 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,889 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7644
2025-07-14 03:25:59,889 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,889 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,889 [INFO] Price data for Bioplastic: buy_price=5359, sell_price=7229
2025-07-14 03:25:59,889 [INFO] Bioplastic calculation: buy_price=5359, investment_limit=2000000.0, max_scu_by_investment=373, vehicle_scu=388, final_max_scu=373
2025-07-14 03:25:59,889 [INFO] Bioplastic verification: max_scu=373, actual_investment=1998907, within_limit=True
2025-07-14 03:25:59,891 [INFO] Generated 7109 total routes
2025-07-14 03:25:59,893 [INFO] Top route: Bioplastic - Profit: 1100709
